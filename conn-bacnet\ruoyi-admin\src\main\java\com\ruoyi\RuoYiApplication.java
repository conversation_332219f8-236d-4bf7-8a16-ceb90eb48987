package com.ruoyi;

import com.ruoyi.web.service.CheckProcess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class RuoYiApplication
{
    protected final static Logger logger = LoggerFactory.getLogger(RuoYiApplication.class);

    public static void main(String[] args)
    {
        logger.info(" ============ Server Start ============ ");
        CheckProcess checkProcess = new CheckProcess();
        checkProcess.checkAndKillProcess();
        SpringApplication.run(RuoYiApplication.class, args);
        logger.info(" ============ Server Start Success ============ ");
    }
}
