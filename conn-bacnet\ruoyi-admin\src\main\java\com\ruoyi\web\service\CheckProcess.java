package com.ruoyi.web.service;

import io.swagger.models.auth.In;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.net.ServerSocket;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class CheckProcess {

    private Integer port = -1;

    public static Path extractJarFilePath(String path) {
        try {
            // 确保路径是绝对路径，并去掉任何多余的开头斜杠
            if (path.startsWith("/")) {
                path = path.substring(1);
            }

            // 构建正确的 URI 字符串，确保 Windows 路径中的冒号被正确编码
            // 注意：对于 Windows 路径，使用 "file:///" 作为前缀来确保它是分层的
            String uriString = "file:///" + path.replace(":", "|");

            // 创建 URI 并将其转换为 Path
            URI uri = new URI(uriString).normalize();
            return Paths.get(uri).normalize().toAbsolutePath();
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("Invalid file path format: " + path, e);
        }
    }

    public CheckProcess() {
        Yaml yaml = new Yaml();
        String configPath = null;
        try {
            // 获取当前类的 ProtectionDomain 并找到其 CodeSource 位置
            URL location = getClass().getProtectionDomain().getCodeSource().getLocation();
            System.out.println("CheckProcess Original location path: " + location.getPath());
            Path jarPath = null;
            String osName = System.getProperty("os.name");
            if ("jar".equals(location.getProtocol())) {
                // 如果是 jar 协议，我们尝试从 jar URL 中提取出 JAR 文件的路径
                String path = location.getPath();
                if (path != null) {
                    if(osName.toLowerCase().contains("windows")) {
                        path = path.split("file:/")[1]; // 去掉开头的 file:/
                    } else if(osName.toLowerCase().contains("linux")) {
                        path = path.split("file:")[1]; // 去掉开头的 file:/
                    }
                }
                // 解析 jar URL 中的 !/ 分隔符前的部分为 JAR 文件路径
                path = path.split("!/")[0];
                System.out.println("CheckProcess path: " + path.toString());
                configPath = path.substring(0, path.lastIndexOf("/"))+"/config/application.yml";
            } else if ("file".equals(location.getProtocol())) {
                // 如果是 file 协议，则直接转换为文件路径
                configPath = Paths.get(location.toURI()).resolve("application.yml").toString();
            }
            System.out.println("CheckProcess configPath: " + configPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 确认是否有 config/application.yml
        try (FileInputStream fis = new FileInputStream(configPath)) {
            // 加载 YAML 文件
            Map<String, Object> yamlMap = yaml.load(fis);
            // 获取 server 节点
            Map<String, Object> serverMap = (Map<String, Object>) yamlMap.get("server");
            if (serverMap != null) {
                // 获取 port 配置项的值
                Object portObj = serverMap.get("port");
                if (portObj != null) {
                    port = Integer.parseInt(portObj.toString());
                }
            }
        } catch (IOException | NumberFormatException e) {
            InputStream inputStream = CheckProcess.class.getClassLoader().getResourceAsStream("application.yml");
            if (inputStream != null) {
                try {
                    // 加载 YAML 文件
                    Map<String, Object> yamlMap = yaml.load(inputStream);
                    // 获取 server 节点
                    Map<String, Object> serverMap = (Map<String, Object>) yamlMap.get("server");
                    if (serverMap != null) {
                        // 获取 port 配置项的值
                        Object portObj = serverMap.get("port");
                        if (portObj != null) {
                            port = Integer.parseInt(portObj.toString());
                        }
                    }
                } finally {
                    try {
                        inputStream.close();
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            } else {
                System.out.println("CheckProcess 未找到 application.yml 文件");
            }
        }
    }

    public void checkAndKillProcess() {
        System.out.println("CheckProcess checkAndKillProcess " + port);
        // 检查当前端口是否被占用，并杀掉占用进程
        Boolean portOpened = isPortAvailable(port);
        System.out.println("CheckProcess portOpened " + portOpened);
        if (!portOpened) {
            killProcess(port);
        }
    }

    private boolean isPortAvailable(int port) {
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            serverSocket.close();
            return true;
        } catch (IOException e) {
            // 端口已被占用
            return false;
        }
    }

    private void killProcess(int port) {
        String osName = System.getProperty("os.name");
        List<String> pids = new ArrayList<>();
        if(osName.toLowerCase().contains("windows")) {
            pids = findProcessesUsingPortInWindows(port);
            killProcessInWindows(pids);
        } else if(osName.toLowerCase().contains("linux")) {
            pids = findProcessesUsingPortInLinux(port);
            killProcessInLinux(pids);
        }
    }

    public List<String> findProcessesUsingPortInWindows(int port) {
        List<String> pids = new ArrayList<>();
        try {
            // 使用系统的 shell 来解析命令
            ProcessBuilder pb = new ProcessBuilder("cmd.exe", "/c", "netstat -ano | findstr " + port);
            pb.redirectErrorStream(true); // 合并标准输出和错误流
            Process process = pb.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("findProcessesUsingPortInWindows " + line);
                // 解析获取PID，假设输出格式为 "  TCP    0.0.0.0:8080           0.0.0.0:0              LISTENING       1234"
                String[] parts = line.split("\\s+");
                if (parts.length > 4) {
                    String pid = parts[5];
                    pids.add(pid);
                    System.out.println("findProcessesUsingPortInWindows. pid " + pid);
                }
            }
            reader.close();
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> uniqueSet = new HashSet<>(pids);
        List<String> uniquePids = new ArrayList<>(uniqueSet);
        return uniquePids;
    }

    private List<String> findProcessesUsingPortInLinux(int port) {
        List<String> pids = new ArrayList<>();
        try {
            Process process = Runtime.getRuntime().exec("netstat -tlnp");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(":" + port + " ")) { // 注意这里的格式可能需要根据实际输出调整
                    System.out.println("findProcessesUsingPortInLinux line: " + line);
                    // 在这里解析获取PID，假设输出格式为 "tcp6       0      0 :::8080                 :::*                    LISTEN      进程ID/程序名 "
                    int endIndex = line.lastIndexOf("/");
                    int startIndex = line.substring(0, endIndex).lastIndexOf(" ");
                    System.out.println("findProcessesUsingPortInLinux endIndex " + endIndex + " startIndex " + startIndex);
                    if (startIndex < endIndex) {
                        String pid = line.substring(startIndex, endIndex);
                        pids.add(pid);
                        System.out.println("findProcessesUsingPortInLinux. pid " + pid);
                    }
                }
            }
            reader.close();
            process.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        Set<String> uniqueSet = new HashSet<>(pids);
        List<String> uniquePids = new ArrayList<>(uniqueSet);
        return uniquePids;
    }

    private void killProcessInWindows(List<String> pids) {
        if(null != pids && pids.size() > 0) {
            for (String pid : pids) {
                try {
                    String command = "taskkill /F /PID " + pid;
                    Runtime.getRuntime().exec(command);
                    System.out.println("killProcessInWindows success. taskkill /F /PID " + pid);
                } catch (IOException e) {
                    System.out.println("killProcessInWindows failed. pid: " + pid);
                    e.printStackTrace();
                }
            }
        }
    }

    private void killProcessInLinux(List<String> pids) {
        if(null != pids && pids.size() > 0) {
            for (String pid : pids) {
                try {
                    String command = "kill -9 " + pid;
                    Runtime.getRuntime().exec(command);
                    System.out.println("killProcessInLinux success. kill -9 " + pid);
                } catch (IOException e) {
                    System.out.println("killProcessInLinux failed. pid: " + pid);
                    e.printStackTrace();
                }
            }
        }
    }

}
