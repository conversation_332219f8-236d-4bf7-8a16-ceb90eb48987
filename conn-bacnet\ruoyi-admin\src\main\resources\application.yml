# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.8
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  address: 0.0.0.0
  port: 8049
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ************************************************************************************************************************************************************************************
        db: ly_ibms
        username: llproj
        password: hrPFkMbN1X95I6+Hr+oj7U8RCyQA6ZqUfbkqJudu3wukK/tnq+jPbn0hAeUULyJQVfFGN375J/XbLPN5egdxPQ==
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      filters:
        config:
          enabled: true
      connect-properties:
        config.decrypt: true
        config.decrypt.key: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJqpaNwIrVmhR949NifetL3su/W6VWien3OtSlexYrgEdybUKFER8kt8Snesn8aHw2NE3EjdXx761qWjJbvGoSsCAwEAAQ==
  # redis 配置
  redis:
    # 地址
    host: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com
    port: 6379
    username: hzabc
    password: PgMbdLHe88tkFnPAgnsnYsDNbrn6PcUqwf0RtRT3HuZ1R7ssz265XpLqlg+0xaMo8TgzI4p2J4q7VAx7BWMPzw==
    database: 122
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
    connect-properties:
      config.decrypt: true
      config.decrypt.key: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJqpaNwIrVmhR949NifetL3su/W6VWien3OtSlexYrgEdybUKFER8kt8Snesn8aHw2NE3EjdXx761qWjJbvGoSsCAwEAAQ==

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

quartz:
  switch: true
  poolSize: 200

bacnet:
  enabledJob: true
  enabledRead: false
  enabledWrite: false
  enabledCov: false
  localIp: *************
  subnet: *************
  udpPort: 47808
  instanceNum: 911
  # 绑定的采集器Id，支持多个
  collectorIds: 7
  # 采集点太多时，分多次采集，每次采的点的最大个数
  packSize: 100
  # 每轮采集最大超时时间，单位毫秒，防止因为网络不稳引起无返回造成程序挂起
  collectTimeout: 45000
  # 每条命令超时时间，单位毫秒
  cmdTimeout: 500
  # 设置 扫描设备广播发出后，等待设备应答的时长
  scanDeviceWaiting: 2
  # 设置 每读取 N 次数据后，再扫描一遍在线设备
  scanDeviceListSpan: 10
  # 多次请求超时时，触发重连
  maxTimeoutRestartCount: 29
  # 注册外部设备超时时间，填多填少，不影响采集，可以就用这个默认值
  remoteRegisterTimeout: 10000000
  # 总过重连多少次时，就主动触发一次关闭重启
  maxReconnectCount: 50
  # 对于不在一个网段的外部设备，需要主动注册。如果外部多个设备位于同-个网段，只用注册一个主设备，会自动把其他设备一起扫描出来
  remoteRegisterList:
#    - ip: *************
#      port: 47808
#      deviceId: 110042
#    - ip: *************
#      port: 47808
#      deviceId: 110043

opcda:
  enabledJob: false
#  server: **************
#  domain: localhost
#  user: opcuser
#  password: lanxing121!
#  progId: Kepware.KEPServerEX.V6
#  clsid: 7BC0CC8E-482C-47CA-ABDC-0FE7F9C6E729
#  # 绑定的采集器Id，支持多个
#  collectorIds: 13
#  # 采集点太多时，分多次采集，每次采的点的最大个数
#  packSize: 100
#  # 每轮采集最大超时时间，单位毫秒，防止因为网络不稳引起无返回造成程序挂起
#  collectTimeout: 30000


# 推送采集数据给 api 入库
dataserver:
  enabled: true
  # 数据采集完成，一起打包上报入库
  sendPacks: true
  url: http://localhost:8079/data
