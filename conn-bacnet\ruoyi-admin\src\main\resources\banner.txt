Application Version: ${ruoyi.version}
Spring Boot Version: ${spring-boot.version}
////////////////////////////////////////////////////////////////////
Server:
    port: ${server.port}
    context-path: ${server.servlet.context-path}

mysql:
    url: ${spring.datasource.druid.master.url}
    username: ${spring.datasource.druid.master.username}

Redis:
   host: ${spring.redis.host}
   database: ${spring.redis.database}

bacnet:
    enabledJob: ${bacnet.enabledJob}
    localIp: ${bacnet.localIp}
    udpPort: ${bacnet.udpPort}
    collectorIds: ${bacnet.collectorIds}
    instanceNum: ${bacnet.instanceNum}
    scanDeviceWaiting: ${bacnet.scanDeviceWaiting}
    scanDeviceListSpan: ${bacnet.scanDeviceListSpan}

opcda:
    enabledJob: ${opcda.enabledJob}
    server: ${opcda.server}
    user: ${opcda.user}
    progId: ${opcda.progId}
    clsid: ${opcda.clsid}
    collectorIds: ${opcda.collectorIds}

dataserver:
    enabledJob: ${dataserver.enabled}
    url: ${dataserver.url}
////////////////////////////////////////////////////////////////////
