package com.ruoyi.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Data
@ConfigurationProperties(prefix = "bacnet", ignoreInvalidFields = true)
public class BacnetConfig {
    private boolean enabledJob = false;
    private boolean enabledRead = false;
    private boolean enabledWrite = false;
    private boolean enabledCov = false;
    private Integer packSize = 100;
    private String collectorIds = "";
    private String localIp = "127.0.0.1";
    private String subnet = "*************";
    private Integer instanceNum = 911;
    private Integer udpPort = 47808;
    private Integer collectTimeout = 30000;
    private Integer cmdTimeout = 1000;
    private Integer scanDeviceWaiting = 2;
    private Integer scanDeviceListSpan = 10;
    private Integer maxTimeoutRestartCount = 29;  // 一次轮询，29次超时即尝试重连
    private Integer maxReconnectCount = 50;       // 重连多少次后，关闭进程等待重启
    private Integer remoteRegisterTimeout = 10000000;
    private List<RemoteRegister> remoteRegisterList = new ArrayList<>();
}

