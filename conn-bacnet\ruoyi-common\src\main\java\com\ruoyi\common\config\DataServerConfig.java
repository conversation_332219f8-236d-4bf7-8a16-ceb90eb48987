package com.ruoyi.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "dataserver", ignoreInvalidFields = true)
public class DataServerConfig {
    private boolean enabled = false;
    private boolean sendPacks = false;
    private String url = "http://loalhost:8079/data";
}
