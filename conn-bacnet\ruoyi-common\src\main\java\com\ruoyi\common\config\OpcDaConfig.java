package com.ruoyi.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "opcda", ignoreInvalidFields = true)
public class OpcDaConfig {
    private boolean enabledJob = false;
    private String server = "";
    private String domain = "";
    private String user = "";
    private String password = "";
    private String progId = "";
    private String clsid = "";
    private String collectorIds;
    private Integer packSize = 100;
    private Integer collectTimeout = 30000;

}