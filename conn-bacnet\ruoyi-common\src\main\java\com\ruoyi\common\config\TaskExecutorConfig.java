package com.ruoyi.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.HashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池、异步任务、定时调度相关配置
 * @version 1.0
 * @date
 */
@Configuration
@EnableAsync
@EnableScheduling
public class TaskExecutorConfig implements SchedulingConfigurer {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    // 是否关闭定时任务开关
    @Value("${quartz.switch:true}")
    private boolean taskScheduling;

    @Value("${quartz.poolSize:200}")
    private int poolSize;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        if(!taskScheduling){
            //清空扫描到的定时任务即可
            taskRegistrar.setTriggerTasks(new HashMap());
            taskRegistrar.setCronTasks(new HashMap());
            taskRegistrar.setFixedRateTasks(new HashMap());
            taskRegistrar.setFixedDelayTasks(new HashMap());
            logger.warn("【conn-bacnet】 scheduling false. no schedule start");
        }
        taskRegistrar.setScheduler(taskExecutor());
    }

    /**
     * 自定义定时任务线程池
     * @return 线程池
     */
    @Bean(name = "myThreadPoolTaskExecutor")
    public TaskExecutor getMyThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(20);
        taskExecutor.setMaxPoolSize(poolSize);
        taskExecutor.setQueueCapacity(25);
        taskExecutor.setKeepAliveSeconds(200);
        taskExecutor.setThreadNamePrefix("ThreadPool-");
        // 线程池对拒绝任务（无线程可用）的处理策略，目前只支持AbortPolicy、CallerRunsPolicy；默认为后者
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //调度器shutdown被调用时等待当前被调度的任务完成
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        //等待时长
        taskExecutor.setAwaitTerminationSeconds(60);
        taskExecutor.initialize();
        return taskExecutor;
    }

    // 任务池
    @Bean(destroyMethod = "shutdown")
    public Executor taskExecutor() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(poolSize);
        taskScheduler.initialize();
        return taskScheduler;
    }
}
