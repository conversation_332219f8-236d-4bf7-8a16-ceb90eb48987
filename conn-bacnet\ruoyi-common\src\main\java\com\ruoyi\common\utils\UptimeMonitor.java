package com.ruoyi.common.utils;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;

public class UptimeMonitor {

    static RuntimeMXBean rb = ManagementFactory.getRuntimeMXBean();

    public static long getUptime() {
        return rb.getUptime() / 1000; // 秒
    }
    public static String getFormattedUptime() {
        long uptime = rb.getUptime();
        long seconds = uptime / 1000 % 60;
        long minutes = uptime / (1000 * 60) % 60;
        long hours = uptime / (1000 * 60 * 60) % 24;
        long days = uptime / (1000 * 60 * 60 * 24);
        return String.format("%d天 %02d:%02d:%02d", days, hours, minutes, seconds);
    }
}

