<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>ruoyi</artifactId>
		<groupId>com.ruoyi</groupId>
		<version>3.8.8</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>ruoyi-dataCollector</artifactId>

	<description>
		dataCollector 工具
	</description>

	<properties>
		<skipTests>true</skipTests>
	</properties>

	<dependencies>
		<!-- SpringBoot Web容器 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-common</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--hutool -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.2</version>
		</dependency>

		<!-- bacnet -->
		<dependency>
			<groupId>com.infiniteautomation</groupId>
			<artifactId>bacnet4j</artifactId>
			<version>6.0.0</version>
		</dependency>

		<!--opcda -->
		<dependency>
			<groupId>org.openscada.utgard</groupId>
			<artifactId>org.openscada.opc.lib</artifactId>
			<version>1.5.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.65</version>
		</dependency>
		<dependency>
			<groupId>org.openscada.utgard</groupId>
			<artifactId>org.openscada.opc.dcom</artifactId>
			<version>1.5.0</version>
		</dependency>



	</dependencies>

	<repositories>
		<repository>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
			<id>ias-snapshots</id>
			<name>Infinite Automation Snapshot Repository</name>
			<url>https://maven.mangoautomation.net/repository/ias-snapshot/</url>
		</repository>
		<repository>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<id>ias-releases</id>
			<name>Infinite Automation Release Repository</name>
			<url>https://maven.mangoautomation.net/repository/ias-release/</url>
		</repository>
	</repositories>

</project>
