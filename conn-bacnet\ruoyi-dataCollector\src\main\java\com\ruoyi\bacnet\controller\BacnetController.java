package com.ruoyi.bacnet.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.bacnet.service.BacnetService;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;



import org.springframework.web.client.RestTemplate;

@RestController
@RequestMapping("/test/bacnet/api")
public class BacnetController {

    @Autowired
    private BacnetService bacnetService;

    @RequestMapping(value="/startCollect")
    @ResponseBody
    public AjaxResult sync(HttpServletResponse response,
                           HttpServletRequest request) throws Exception
    {
        Map res = new HashMap<>();
        if(null != res && res.equals("OK")) {
            return AjaxResult.success("success", res);
        } else {
            return AjaxResult.error("error");
        }
    }

    @RequestMapping(value="/testPost")
    @ResponseBody
    public AjaxResult testPost(HttpServletResponse response,
                               HttpServletRequest request) throws Exception
    {
        String url = "http://localhost:8001/data";
        String json = "{\"id\":*********,\"val\":\"0\",\"calc\":1,\"updatedAt\":1734597287887,\"params\":{}}";
//        String res = HttpRequest.post(url)
//                //.headerMap(heads, false)
//                .body(json)
//                .execute().body();
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setReadTimeout(5000);//单位为ms
        factory.setConnectTimeout(5000);//单位为ms
        RestTemplate restTemplate = new RestTemplate(factory);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(json, headers);
        String res = restTemplate.postForObject(url, entity, String.class);
        return AjaxResult.success("success", res);
    }
}