package com.ruoyi.bacnet.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.ruoyi.bacnet.utils.BACnetUtil;
import com.ruoyi.bacnet.utils.IpUtil;
import com.ruoyi.base.domain.ItemData;
import com.ruoyi.base.service.ItemDataService;
import com.ruoyi.common.config.BacnetConfig;
import com.ruoyi.common.config.DataServerConfig;
import com.ruoyi.common.utils.StringUtils;
import com.serotonin.bacnet4j.LocalDevice;
import com.serotonin.bacnet4j.RemoteDevice;
import com.serotonin.bacnet4j.exception.BACnetException;
import com.serotonin.bacnet4j.exception.BACnetTimeoutException;
import com.serotonin.bacnet4j.exception.PropertyValueException;
import com.serotonin.bacnet4j.npdu.ip.IpNetwork;
import com.serotonin.bacnet4j.npdu.ip.IpNetworkBuilder;
import com.serotonin.bacnet4j.service.confirmed.SubscribeCOVRequest;
import com.serotonin.bacnet4j.transport.DefaultTransport;
import com.serotonin.bacnet4j.type.Encodable;
import com.serotonin.bacnet4j.type.enumerated.ObjectType;
import com.serotonin.bacnet4j.type.enumerated.PropertyIdentifier;
import com.serotonin.bacnet4j.type.primitive.*;
import com.serotonin.bacnet4j.type.primitive.Boolean;
import com.serotonin.bacnet4j.type.primitive.Double;
import com.serotonin.bacnet4j.util.*;
import lohbihler.warp.WarpClock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.util.*;
import java.util.Date;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
public class BacnetService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BacnetConfig bacnetConfig;

    @Autowired
    private DataServerConfig dataServerConfig;

    @Autowired
    private ItemDataService itemDataService;

    private IpNetwork ipNetwork;
    // 采集客户端实例
    private LocalDevice localDevice;

    // collectIdList
    private List<String> collectorIdList;

    // 需要采集的设备列表
    private Map<Integer, RemoteDevice> remoteDevices;
    private List<RemoteDevice> remoteDeviceList;
    private List<Integer> remoteDeviceIdList;
    private Map<Integer, List<Map>> deviceDatas;
    private List<Map> itemDataList = new ArrayList<>();
    private List<Long> covItemIdList = new ArrayList<>();
    Map<String, Map> itemDataMap = new HashMap();
    private Long readCount = 0l; // 记录已经循环读取了多少次, 不会在关闭时主动重置，重启时重置

    private Long maxTimeoutCount = 10l; // 超时最大错误次数，超过时，休息
    private Long timeoutSleep = 2000l;  // 超过时，休息2秒，单位毫秒
    private Long timeoutCount = 0l;     // 请求超时报错次数，每次轮询重置
    private Long realTimeoutCount = 0l; // 一次轮询，请求超时报错总次数，每次轮询重置
    private java.lang.Boolean needRestart = false;  // 是否需要重连flag
    private Long reconnectCount = 0l;   // 记录重连了多少次，达到上限会主动触发重启整个服务
    private Long writeInReadDevice = 10l;          // 读几个设备后，中间插入一个写指令

    private Long minDeviceNum = 20l;  // 最少有多个设备在线，如果少于这个数量，则每次都进行扫描

    // 关闭并重启采集服务
    public void runing() {
        // 需要这里打印日志，触发延迟加载或初始化操作，否则会被 opcda 的配置覆盖掉
        logger.debug("bacnetConfig " + JSONUtil.toJsonStr(bacnetConfig));
        if(bacnetConfig.isEnabledJob()) {
            logger.debug("runing CompletableFuture<String> futur start -------------------.");
            // 创建异步任务，超时重启采集
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                initCollectData();
                startCollect();
                readCount += 1;
                timeoutCount = 0l;  // 重置错误次数
                realTimeoutCount = 0l; // 重置总错误次数
                logger.info("============ Collect end ============ readCount " + readCount);
                return "initCollectData and startCollect finished.";
            });
            try {
                // 设置超时时间
                String res = future.get(bacnetConfig.getCollectTimeout(), TimeUnit.MILLISECONDS );
                logger.debug("runing success. " + res);
            } catch (TimeoutException e) {
                logger.warn("runing error Timeout error: " + e.getMessage() + " now reconnect.");
                needRestart = true;
                //  closeClient();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt(); // 保留中断状态
                logger.warn("runing was interrupted or failed:" + e.getMessage() + " now reconnect.");
                needRestart = true;
                // closeClient();
            } finally {
                // 确保任务取消（即使已经完成）
                future.cancel(true);
                // closeClient();
            }
            // 主动重新连接服务
            if(needRestart) {
                logger.warn("runing error timeout count need reconnect. " + realTimeoutCount);
                closeClient();
            }
        } else {
            logger.debug("BacnetService enabledJob false");
        }
    }

    public void closeClient() {
        logger.warn("closeClient start");
        try {
            if(null != localDevice) {
                localDevice.terminate();
            }
        } catch (Exception e) {
            logger.warn("closeClient error: " + e.getMessage());
        }
        localDevice = null;
        covItemIdList = new ArrayList<>();
        // readCount = 0l;
        realTimeoutCount = 0l;
        needRestart = false;
        reconnectCount += 1;
        logger.warn("closeClient finished.");
        // 判断是否触发重连上限，需要重启服务
        if(reconnectCount > bacnetConfig.getMaxReconnectCount()) {
            logger.warn("closeClient too many reconnects " + reconnectCount + " more than maxCount " + bacnetConfig.getMaxReconnectCount());
            System.exit(0);
        }
    }

    public void startCollect() {
        try {
            logger.debug("==== inited localDevice start =====");
            if(null == localDevice) {
                logger.debug("==== inited localDevice new localDevice start =====");
                ipNetwork = new IpNetworkBuilder()
                        .withLocalBindAddress(bacnetConfig.getLocalIp()) //
                        .withSubnet(bacnetConfig.getSubnet(), 24)
                        .withPort(bacnetConfig.getUdpPort())
                        .withReuseAddress(true)
                        .build();
                if (null != bacnetConfig.getRemoteRegisterList() && bacnetConfig.getRemoteRegisterList().size() > 0) {
                    ipNetwork.enableBBMD();
                }
                localDevice = new LocalDevice(bacnetConfig.getInstanceNum(), new DefaultTransport(ipNetwork))
                        .withClock(new WarpClock()) //
                        .initialize();

                //添加订阅监听器
                //if(bacnetConfig.isEnabledCov()) {
                    localDevice.getEventHandler().addListener(new CovNotifListener(new CovNotifListenerCallback() {
                        @Override
                        public void process(String deviceId, String func, String instance, String value) {
                            logger.info("CovNotifListenerCallback process " + deviceId + " " + func + " " + instance + " " + value);
                            try {
                                Map point = itemDataMap.get(deviceId + "_" + func + "_" + instance);
                                if(null != point) {
                                    // 如果有 http 服务接收中转，则调用 api 入库
                                    ItemData itemData = new ItemData();
                                    itemData.setVal(value);
                                    itemData.setId(Long.valueOf(point.get("itemDataId").toString()));
                                    itemData.setUpdatedAt(new Date());
                                    itemData.setCalc(1);  // 标记入库时需要通过 funcList 计算
                                    logger.debug("itemData ready " + JSONUtil.toJsonStr(itemData));

                                    if (dataServerConfig.isEnabled()) {
                                        logger.debug("sendPostAPI data c: " + dataServerConfig.getUrl() + " " + JSONUtil.toJsonStr(itemData));
                                        sendPostAPI(dataServerConfig.getUrl(), JSONUtil.toJsonStr(itemData));
                                    } else {
                                        // 直接入库操作
                                        logger.debug("itemData update " + JSONUtil.toJsonStr(itemData));
                                        itemDataService.updateItemData(itemData);
                                    }
                                }
                            } catch (Exception ex) {
                                logger.warn("CovNotifListenerCallback process error. " + ex.getMessage());
                            }
                        }
                    }));
                //}
            }
            logger.info("==== inited localDevice finished =====");

            logger.debug("RemoteRegisterList: " + JSONUtil.toJsonStr(bacnetConfig.getRemoteRegisterList()));
            // 启用 bbmd 模式，
            if (null != bacnetConfig.getRemoteRegisterList() && bacnetConfig.getRemoteRegisterList().size() > 0) {
                ipNetwork.enableBBMD();
                bacnetConfig.getRemoteRegisterList().forEach(remote -> {
                    try {
                        logger.debug("==== registerAsForeignDevice ===== " + remote.getIp() + " " + remote.getPort() + " " + remote.getDeviceId());
                        ipNetwork.unregisterAsForeignDevice();
                        ipNetwork.registerAsForeignDevice(new InetSocketAddress(remote.getIp(), remote.getPort()), bacnetConfig.getRemoteRegisterTimeout());
                        RemoteDevice remoteDevice = localDevice.getRemoteDeviceBlocking(remote.getDeviceId());
                        // 下一行的方法为jar包提供的工具方法，所做的内容就是通过 localDevice.send 获取属性值并赋值到remoteDevice上，以便直接通过remoteDevice.get...的方式调用
//                        DiscoveryUtils.getExtendedDeviceInformation(localDevice, remoteDevice);
                        logger.debug("==== registerAsForeignDevice added ===== " + remoteDevice.getName() + remoteDevice.getAddress() + remoteDevice.getInstanceNumber());
                        // 扫描所有设备
                        remoteDeviceList = getAllRemoteDevices(IpUtil.getBroadcastByIpAndSubnet(bacnetConfig.getLocalIp(), bacnetConfig.getSubnet()));
                        remoteDeviceIdList = remoteDeviceList.stream()
                                .map(rd -> rd.getObjectIdentifier().getInstanceNumber())
                                .collect(Collectors.toList());
                        // 启动采集
                        initCollectDataList();
                        readValues();
                        writeValues();
                        ipNetwork.unregisterAsForeignDevice();
                        logger.debug("==== registerAsForeignDevice finished and unregisterAsForeignDevice success ===== ");
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.debug("**** registerAsForeignDevice failed. " + e.getMessage());
                    }
                });
            } else {
                // 如果在线设备个数少于 minDeviceNum ，则每次都进行扫描
                if(readCount % bacnetConfig.getScanDeviceListSpan() == 0 || (null != remoteDeviceList && remoteDeviceList.size() < minDeviceNum )) {
                    // 普通模式 直接本地扫描病采集
                    // 通过udp广播，获取本网段内的所有设备
                    String subnet = bacnetConfig.getSubnet(); // IpUtil.getSubnet(); // 本机子网掩码
                    logger.debug("============ " + bacnetConfig.getLocalIp() + " " + subnet + " 普通模式 直接本地扫描病采集 通过udp广播，获取本网段内的所有设备");
                    remoteDeviceList = getAllRemoteDevices(IpUtil.getBroadcastByIpAndSubnet(bacnetConfig.getLocalIp(), subnet));
                    remoteDeviceIdList = remoteDeviceList.stream()
                            .map(rd -> rd.getObjectIdentifier().getInstanceNumber())
                            .collect(Collectors.toList());
                    // 启动采集
                    initCollectDataList();
                }
                readValues();
                writeValues();
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.warn("startCollect error: " + e.getMessage());
        }
    }

    // 初始化采集服务 TODO remove
    public void initService() {
        // 准备 采集器列表
        String collectorIds = bacnetConfig.getCollectorIds();
        if(null != collectorIds && StringUtils.isNotEmpty(collectorIds)) {
            collectorIdList = Arrays.asList(collectorIds.split(","));
        }
        // 准备采集服务客户端
        if(null == localDevice) {
            LocalDevice d = null;
            try {
                //创建网络对象
                IpNetwork ipNetwork = BACnetUtil.initIpNetwork(bacnetConfig.getLocalIp(), bacnetConfig.getSubnet(), bacnetConfig.getUdpPort());
                localDevice = BACnetUtil.initLocalDevice(ipNetwork, bacnetConfig.getInstanceNum());

                // 通过udp广播，获取本网段内的所有设备
                String subnet = bacnetConfig.getSubnet(); // IpUtil.getSubnet(); // 本机子网掩码
                logger.debug("============" + bacnetConfig.getLocalIp() + subnet);
                List<RemoteDevice> remoteDevices = getAllRemoteDevices(IpUtil.getBroadcastByIpAndSubnet(bacnetConfig.getLocalIp(), subnet));

            } catch (Exception e) {
                e.printStackTrace();
                if (d != null) {
                    d.terminate();
                }
                localDevice = null;
            }
        }
    }

    // 带远程注册的初始化 TODO remove
    public void initServiceBBMD() throws Exception {
        // 准备采集服务客户端
        if(null == localDevice) {
            LocalDevice d = null;
            try {
                ipNetwork = new IpNetworkBuilder()
                        .withLocalBindAddress(bacnetConfig.getLocalIp()) //
                        .withSubnet(bacnetConfig.getSubnet(), 24)
                        .withPort(bacnetConfig.getUdpPort())
                        .withReuseAddress(true)
                        .build();
                ipNetwork.enableBBMD();
                localDevice = new LocalDevice(bacnetConfig.getInstanceNum(), new DefaultTransport(ipNetwork))
                        .withClock(new WarpClock()) //
                        .initialize();
                logger.info("==== inited localDevice finished =====");

                if (null != bacnetConfig.getRemoteRegisterList() && bacnetConfig.getRemoteRegisterList().size() > 0) {
                    registerAsForeignDevice();
                }
                // 通过udp广播，获取本网段内的所有设备
                String subnet = bacnetConfig.getSubnet(); // IpUtil.getSubnet(); // 本机子网掩码
                logger.debug("============" + bacnetConfig.getLocalIp() + " " + subnet);
                List<RemoteDevice> remoteDeviceList = getAllRemoteDevices(IpUtil.getBroadcastByIpAndSubnet(bacnetConfig.getLocalIp(), subnet));

                if (bacnetConfig.isEnabledCov()) {
                    initCovDataList();
                }

            } catch (Exception e) {
                e.printStackTrace();
                if (d != null) {
                    d.terminate();
                }
                localDevice = null;
            }
        }

//        测试设备数据读取
//        logger.info("==== registerAsForeignDevice ===== " + "*************" + " " + 47808 + " " + 110040 );
//        //ipNetwork.registerAsForeignDevice(new InetSocketAddress("*************", 47808), 10000000); // 不能多次重复注册
//        RemoteDevice remoteDevice = localDevice.getRemoteDeviceBlocking(110040);
//        logger.info("getName：{}", remoteDevice.getName());
//        logger.info("modelName：{}", remoteDevice.getModelName());
//        logger.info("instanceNumber：{}", remoteDevice.getObjectIdentifier().getInstanceNumber());
//        logger.info("ObjectType：{}", remoteDevice.getObjectIdentifier().getObjectType().toString());
//        logger.info("objectName：{}", remoteDevice.getDeviceProperty(PropertyIdentifier.objectName));
//        logger.info("vendorName：{}", remoteDevice.getDeviceProperty(PropertyIdentifier.vendorName));
//        logger.info("profileName：{}", remoteDevice.getDeviceProperty(PropertyIdentifier.profileName));
//        logger.info("authenticationPolicyNames：{}", remoteDevice.getDeviceProperty(PropertyIdentifier.authenticationPolicyNames));
//        logger.info("userName：{}", remoteDevice.getDeviceProperty(PropertyIdentifier.userName));
//        logger.info("groupMemberNames：{}", remoteDevice.getDeviceProperty(PropertyIdentifier.groupMemberNames));
//        logger.info("networkInterfaceName：{}", remoteDevice.getDeviceProperty(PropertyIdentifier.networkInterfaceName));
//
//        logger.debug("============================ 1");
//        RemoteDevice remoteDevice2 = localDevice.getRemoteDeviceBlocking(110042); // 不能多次重复注册
//        logger.info("getName：{}", remoteDevice2.getName());
//        logger.info("modelName：{}", remoteDevice2.getModelName());
//        logger.info("instanceNumber：{}", remoteDevice2.getObjectIdentifier().getInstanceNumber());
//        logger.info("ObjectType：{}", remoteDevice2.getObjectIdentifier().getObjectType().toString());
//        logger.info("objectName：{}", remoteDevice2.getDeviceProperty(PropertyIdentifier.objectName));
//
//        logger.debug("============================ 2");
//
//        String subnet = bacnetConfig.getSubnet();
//        List<RemoteDevice> remoteDevices = getAllRemoteDevices(IpUtil.getBroadcastByIpAndSubnet(bacnetConfig.getLocalIp(), subnet));
//
//        logger.debug("============================ 3");
//
//        String funcCode = "analog-input";
//        Integer instanceNumber = 1001;
//        logger.debug("============================ read value " + " analog-input " + instanceNumber);
//        ObjectIdentifier oid = new ObjectIdentifier(objectType(funcCode), instanceNumber);
//        logger.debug("[read point] before oid:" + JSONUtil.toJsonStr(oid));
//        PropertyValues pvs1 = RequestUtils.readOidPresentValues(localDevice, remoteDevice,
//                Arrays.asList(oid),null);
//        logger.debug("[read point] res pvs2:" + JSONUtil.toJsonStr(pvs1));
//        logger.debug("[read point] oid:" + oid.getInstanceNumber() + " value:"
//                + pvs1.get(oid, PropertyIdentifier.presentValue)
//                + "  " + oid.getObjectType() + "_" + oid.getInstanceNumber());
//        Encodable initValue1 = pvs1.get(oid, PropertyIdentifier.presentValue);
//        logger.debug("[read point] initValue:" + initValue1.toString());
//        logger.debug("============================ read remote 1 value " + " analog-input " + instanceNumber);
//
//        logger.debug("[read point] before oid:" + JSONUtil.toJsonStr(oid));
//        PropertyValues pvs2 = RequestUtils.readOidPresentValues(localDevice, remoteDevice2,
//                Arrays.asList(oid),null);
//        logger.debug("[read point] res pvs2:" + JSONUtil.toJsonStr(pvs2));
//        logger.debug("[read point] oid:" + oid.getInstanceNumber() + " value:"
//                + pvs2.get(oid, PropertyIdentifier.presentValue)
//                + "  " + oid.getObjectType() + "_" + oid.getInstanceNumber());
//        Encodable initValue2 = pvs2.get(oid, PropertyIdentifier.presentValue);
//        logger.debug("[read point] initValue:" + initValue2.toString());
//        logger.debug("============================ read remote 2 value " + " analog-input " + instanceNumber);
//        logger.debug("============================ finished");
    }

    private List<RemoteDevice> getAllRemoteDevices(String networkSegment) throws InterruptedException {
        logger.debug("************************** 获取本网段所有的设备 " + networkSegment + " ****************************");
        RemoteDeviceDiscoverer remoteDeviceDiscoverer = new RemoteDeviceDiscoverer(localDevice);
        remoteDeviceDiscoverer.start();
        remoteDevices = new HashMap<>();
        // 等待网段内的设备响应广播消息
        Thread.sleep(bacnetConfig.getScanDeviceWaiting() * 1000);

        List<RemoteDevice> remoteDeviceList = remoteDeviceDiscoverer.getRemoteDevices();
        logger.info("************************** remoteDevices size " + remoteDeviceList.size() + " ****************************");
        remoteDeviceList.forEach(remoteDevice -> {
            // 不需要重新读取设备信息，直接采集数据
            Integer deviceId = remoteDevice.getObjectIdentifier().getInstanceNumber();
            remoteDevices.put(deviceId, remoteDevice);
            // 创建异步任务，超时重启采集
            /*
            try {
                // 下一行的方法为jar包提供的工具方法，所做的内容就是通过 localDevice.send 获取属性值并赋值到remoteDevice上，以便直接通过remoteDevice.get...的方式调用
                DiscoveryUtils.getExtendedDeviceInformation(localDevice, remoteDevice);
                logger.debug("remoteDevice getModelName " + remoteDevice.getModelName() + " getName " + remoteDevice.getName() + " getVendorName " + remoteDevice.getVendorName());
                remoteDevices.put(deviceId, remoteDevice);
                //return deviceId + "sucess";
            } catch (BACnetException e) {
                e.printStackTrace();
                //return deviceId + "failed";
            }
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 下一行的方法为jar包提供的工具方法，所做的内容就是通过 localDevice.send 获取属性值并赋值到remoteDevice上，以便直接通过remoteDevice.get...的方式调用
                    DiscoveryUtils.getExtendedDeviceInformation(localDevice, remoteDevice);
                    logger.debug("remoteDevice getModelName " + remoteDevice.getModelName() + " getName " + remoteDevice.getName() + " getVendorName " + remoteDevice.getVendorName());
                    remoteDevices.put(remoteDevice.getInstanceNumber(), remoteDevice);
                    return deviceId + "sucess";
                } catch (BACnetException e) {
                    e.printStackTrace();
                    return deviceId + "failed";
                }
            });
            try {
                // 设置超时时间
                String res = future.get(1000, TimeUnit.MILLISECONDS );
                logger.warn("remoteDevice getModelName " + res);
            } catch (TimeoutException e) {
                logger.warn("remoteDevice getModelName, Timeout. deviceId " + deviceId);
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt(); // 保留中断状态
                System.out.println("remoteDevice getModelName error was interrupted or failed: " + e.getMessage());
            } finally {
                // 确保任务取消（即使已经完成）
                future.cancel(true);
            }
            */
        });
        remoteDeviceDiscoverer.stop();
        logger.debug("************************** remoteDeviceDiscoverer stop ");
        return remoteDeviceList;
    }

    // 获取远程设备
    public RemoteDevice getRemoteDevice(Integer deviceId) {
        logger.debug("************************** getRemoteDevice start " + deviceId);
        final RemoteDevice[] rd = {null};
        // 创建异步任务
        CompletableFuture<RemoteDevice> future = CompletableFuture.supplyAsync(() -> {
            try {
                rd[0] = RemoteDeviceFinder.findDevice(localDevice, deviceId).get();
                // 获取扩展设备信息
                DiscoveryUtils.getExtendedDeviceInformation(localDevice, rd[0]);
            } catch (BACnetException e) {
                logger.warn("getRemoteDevice error. deviceId: " + deviceId + " err: " + e.getMessage());
            }
            return rd[0];
        });
        try {
            // 设置超时时间
            rd[0] = future.get(bacnetConfig.getCmdTimeout(), TimeUnit.MILLISECONDS );
        } catch (Exception e) {
            logger.warn("getRemoteDevice error. deviceId: " + deviceId + " err: " + e.getMessage());
        }
        logger.debug("************************** getRemoteDevice end " + deviceId);
        return rd[0];
    }

    // 注册远程设备
    public List<RemoteDevice> registerAsForeignDevice() throws InterruptedException {
        logger.debug("==== registerAsForeignDevice start ===== ");
        Integer ttl = bacnetConfig.getRemoteRegisterTimeout();
        List<RemoteDevice> remoteDeviceList = new ArrayList<>();
        bacnetConfig.getRemoteRegisterList().forEach(remote -> {
            try {
                logger.debug("==== registerAsForeignDevice ===== " + remote.getIp() + " " + remote.getPort() + " " + remote.getDeviceId());
                ipNetwork.registerAsForeignDevice(new InetSocketAddress(remote.getIp(), remote.getPort()), ttl);
                RemoteDevice remoteDevice = localDevice.getRemoteDeviceBlocking(remote.getDeviceId());
                // 下一行的方法为jar包提供的工具方法，所做的内容就是通过 localDevice.send 获取属性值并赋值到remoteDevice上，以便直接通过remoteDevice.get...的方式调用
//                DiscoveryUtils.getExtendedDeviceInformation(localDevice, remoteDevice);
                remoteDeviceList.add(remoteDevice);
                logger.debug("==== registerAsForeignDevice added ===== " + remoteDevice.getName() + remoteDevice.getAddress() + remoteDevice.getInstanceNumber());
            } catch (Exception e) {
                logger.debug("**** registerAsForeignDevice failed. " + e.getMessage());
            }
        });
        logger.debug("registerAsForeignDevice remoteDeviceList size " + remoteDeviceList.size());
        return remoteDeviceList;
    }

    // 启动 cov 监听函数 TODO remove
    public void initCovDataList() {
        if(null != itemDataList && itemDataList.size() == 0) {
            logger.debug("handleCov exit, cause itemList is null. ");
            return;
        }
        // 过滤出需要订阅的点
        List<Map> filteredList = itemDataList;

        if(null != filteredList && filteredList.size() == 0) {
            logger.debug("handleCov exit, cause filteredList is null. funcList not has cov tag");
            return;
        }

        //添加监听器
        localDevice.getEventHandler().addListener(new CovNotifListener(null));
        for(Map itemData : filteredList) {
            Integer deviceId = Integer.valueOf(itemData.get("deviceId").toString());
            Integer instanceNumber = Integer.valueOf(itemData.get("addr").toString());
            ObjectType objectType = objectType(itemData.get("funcCode").toString());

            RemoteDevice rd = remoteDevices.get(deviceId);
            try {
                //发送订阅COV报文 对应为订阅标识（不可为0）,订阅对象,是否要发送确认报文,订阅时长(0为永久)
                localDevice.send(rd, new SubscribeCOVRequest(new UnsignedInteger(deviceId),
                        new ObjectIdentifier(objectType, instanceNumber), Boolean.TRUE,
                        new UnsignedInteger(0))).get();
            } catch (Exception e) {
                logger.warn("handleCov error " + deviceId + "  ");
            }
        }

    }

    // 载入初始化采集数据
    public void initCollectData() {
        logger.debug("initCollectData start. ");
        // 准备 采集器列表
        String collectorIds = bacnetConfig.getCollectorIds();
        if(null != collectorIds && StringUtils.isNotEmpty(collectorIds)) {
            collectorIdList = Arrays.asList(collectorIds.split(","));
        }
        // 载入所有数据点
        if(null != collectorIdList && collectorIdList.size() > 0) {
            itemDataList = new ArrayList<>();
            for(String id: collectorIdList) {
                if(StringUtils.isNotEmpty(id)) {
                    List<Map> dataList = itemDataService.itemDataList(Long.valueOf(id));
                    itemDataList.addAll(dataList);
                }
            }
        }
        // 初始化点位映射
        itemDataMap = new HashMap<>();
        for(Map itemData : itemDataList) {
            itemDataMap.put(itemData.get("deviceId") + "_" + itemData.get("funcCode") + "_" + itemData.get("addr"), itemData);
        }
        logger.debug("initCollectData end. ");
    }

    // 通过item列表，重载需采集的设备
    public void initCollectDataList() {
        logger.debug("initCollectDataList start. ");
        deviceDatas = new HashMap<>();
        List last = null;
        List<Integer> deviceIdList = new ArrayList<>();
        if(null == itemDataList || itemDataList.size() == 0) {
            logger.debug("initCollectDataList exit, cause itemList is null. ");
            return;
        }
        for(Map item: itemDataList) {
            try {
                Integer deviceId = null;
                String deviceIdStr = item.get("deviceId").toString();
                if(StringUtils.isNotEmpty(deviceIdStr)) {
                    deviceId = Integer.valueOf(item.get("deviceId").toString());
                } else {
                    logger.warn("initCollectDataList error. point_talbe.device_id is null. item: " + JSONUtil.toJsonStr(item));
                    continue;
                }
                // 再检查是否添加成功，只增加成功的采集点
                if (remoteDevices.containsKey(deviceId) && null != remoteDevices.get(deviceId)) {
                    List<Map> oids = deviceDatas.get(deviceId);
                    if(null == oids) {
                        oids = new ArrayList<>();
                    }
                    Map oidMap = new HashMap<>();
                    String funcCode = item.get("funcCode").toString();
                    Integer instanceNum = Integer.valueOf(item.get("addr").toString());
                    Long itemId = Long.valueOf(item.get("itemId").toString());
                    Long itemDataId = Long.valueOf(item.get("itemDataId").toString());
                    String itemDataName = item.get("itemDataName").toString();
                    String needCov = item.get("length").toString();
                    oidMap.put("itemId", itemId);
                    oidMap.put("itemDataId", itemDataId);
                    oidMap.put("itemDataName", itemDataName);
                    oidMap.put("oid", (new ObjectIdentifier(objectType(funcCode), instanceNum)));
                    oids.add(oidMap);
                    deviceDatas.put(deviceId, oids);
                    last = oids;

                    //发送订阅COV报文 对应为订阅标识（不可为0）,订阅对象,是否要发送确认报文,订阅时长(0为永久)
                    if(bacnetConfig.isEnabledCov() && !covItemIdList.contains(itemDataId) && "1".equals(needCov)) {
                        logger.debug("SubscribeCOVRequest start deviceId " + deviceId + " funcCode " + funcCode + " instanceNum " + instanceNum);
                        List<Integer> dIdList = new ArrayList();
                        dIdList.add(deviceId);
                        // 创建异步任务，超时重启采集
                        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                            try {
                                localDevice.send(remoteDevices.get(dIdList.get(0)), new SubscribeCOVRequest(new UnsignedInteger(dIdList.get(0)),
                                        new ObjectIdentifier(objectType(funcCode), instanceNum), Boolean.TRUE,
                                        new UnsignedInteger(0))).get();
                                covItemIdList.add(itemDataId);
                                return "success. deviceId " + dIdList.get(0) + " funcCode " + funcCode + " instanceNum " + instanceNum;
                            } catch (Exception e) {
                                e.printStackTrace();
                                logger.warn("handleCov error. deviceId: " + dIdList.get(0) + "  funcCode: " + funcCode + " instanceNum: " + instanceNum);
                                return "failed. deviceId " + dIdList.get(0) + " funcCode " + funcCode + " instanceNum " + instanceNum;
                            }
                        });
                        try {
                            // 设置超时时间
                            String res = future.get(bacnetConfig.getCmdTimeout(), TimeUnit.MILLISECONDS );
                            logger.warn("handleCov " + res);
                        } catch (TimeoutException e) {
                            logger.warn("handleCov error, Timeout. deviceId " + deviceId + " funcCode " + funcCode + " instanceNum " + instanceNum);
                        } catch (InterruptedException | ExecutionException e) {
                            Thread.currentThread().interrupt(); // 保留中断状态
                            System.out.println("handleCov error was interrupted or failed: " + e.getMessage());
                        } finally {
                            // 确保任务取消（即使已经完成）
                            future.cancel(true);
                        }
                        logger.debug("SubscribeCOVRequest end deviceId " + deviceId + " funcCode " + funcCode + " instanceNum " + instanceNum);
//                        try {
//                            localDevice.send(remoteDevices.get(deviceId), new SubscribeCOVRequest(new UnsignedInteger(deviceId),
//                                    new ObjectIdentifier(objectType(funcCode), instanceNum), Boolean.TRUE,
//                                    new UnsignedInteger(0))).get();
//                            item.put("cov", "1");
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                            logger.warn("handleCov error. deviceId: " + deviceId + "  funcCode: " + funcCode + " instanceNum: " + instanceNum);
//                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("initCollectDataList error. item:  " + JSONUtil.toJsonStr(item) + " error: " + e.getMessage());
            }
        }
        logger.debug("remoteDevices " + JSONUtil.toJsonStr(remoteDevices));
        logger.debug("deviceDatas deviceIds: " + JSONUtil.toJsonStr(deviceDatas.keySet()) + " last: " + JSONUtil.toJsonStr(last));
        logger.debug("initCollectDataList end. ");
    }

    // 从已扫描出来的设备列表里面找到 remoteDevice
    public RemoteDevice getRemoteDeviceFromList(Integer deviceId) {
        RemoteDevice rd = null;
        if(null != remoteDeviceList && remoteDeviceList.size() > 0) {
            for(int i=0; i<remoteDeviceList.size(); i++) {
                RemoteDevice crd = remoteDeviceList.get(i);
                String crdId = crd.getObjectIdentifier().toString();
                logger.debug("getRemoteDeviceFromList deviceId " + deviceId + " ObjectIdentifier " + crd.getObjectIdentifier() + " " + deviceId.toString().equals(crdId));
                if(deviceId.toString().equals(crdId)) {
                    rd = crd;
                }
            }
        }
        return rd;
    }

    // 读取数据
    public void readValues() {
        logger.debug("readValues start. ");
        if(!bacnetConfig.isEnabledRead()) {
            logger.debug("readValues end. bacnetConfig is disabled read ");
            return ;
        }
        if(null != localDevice) {
            if(null == deviceDatas || deviceDatas.keySet().size() == 0) {
                logger.debug("readValues exit, cause deviceDatas is null. ");
                return;
            }
            Long readNum = 0l;
            Set<Integer> deviceIds = deviceDatas.keySet();
            // 打乱设备顺序，让后面的也能优先读取
            List<Integer> list = new ArrayList<>(deviceIds);
            Collections.shuffle(list);
            for (Integer deviceId : list) {
                readNum += 1;
                RemoteDevice rd = remoteDevices.get(deviceId);
                List<Map> oidList = deviceDatas.get(deviceId);
                logger.debug("readValues in deviceDatas, deviceId: " + deviceId + " rd: " + JSONUtil.toJsonStr(rd) + " oidList size: " + oidList.size());
                if (oidList.size() > 0) {
                    // list 过大时 需要分批查询结构
                    int packSize = bacnetConfig.getPackSize();
                    int total = oidList.size();
                    double divisionResult = (double) total / packSize;
                    int packNo = (int) Math.ceil(divisionResult);
                    logger.debug("packSize " + packSize + " total " + total + " divisionResult");
                    for (int i = 0; i < packNo; i++) {
                        List<ObjectIdentifier> oids = new ArrayList<>();
                        List<Map> colList = oidList.subList((i * packSize), Math.min(((i + 1) * packSize), total));
                        logger.debug("colList first point =>" + JSONUtil.toJsonStr(colList.get(0)));
                        for (Map oidMap : colList) {
                            // String itemDataId = oidMap.get("itemDataId").toString();
                            ObjectIdentifier oid = (ObjectIdentifier) oidMap.get("oid");
                            oids.add(oid);
                        }
                        try {
                            logger.debug("[read point] before oid size:" + oids.size());
                            CompletableFuture<PropertyValues> future = CompletableFuture.supplyAsync(() -> {
                                try {
                                    PropertyValues pvs2 = RequestUtils.readOidPresentValues(localDevice, rd, oids, null);
                                    return pvs2;
                                } catch (Exception e) {
//                                    e.printStackTrace();
                                    return null;
                                }
                            });
                            try {
                                // 设置超时时间
                                PropertyValues pvs2 = future.get(bacnetConfig.getCmdTimeout(), TimeUnit.MILLISECONDS );
                                if (null != pvs2) {
                                    logger.debug("[read point] res pvs2 size:" + JSONUtil.toJsonStr(pvs2.size()));
                                    List packDataList = new ArrayList();
                                    for (ObjectIdentifier oid : oids) {
                                        Map oidMap = itemDataMap.get(deviceId + "_" + oid.getObjectType() + "_" + oid.getInstanceNumber());
                                        try {
                                            String itemId = oidMap.get("itemId").toString();
                                            String itemDataId = oidMap.get("itemDataId").toString();
                                            String itemDataName = oidMap.get("itemDataName").toString();
                                            logger.debug("read deviceId: " + deviceId + " oid: " + oid.getInstanceNumber()
                                                    + " itemId: " + itemId + " itemDataId: " + itemDataId + " itemDataName: " + itemDataName);
                                            String val = pvs2.get(oid, PropertyIdentifier.presentValue).toString();
                                            // 针对 binary 数据，转换成 0 和 1
                                            if (oid.getObjectType().toString().contains("binary")) {
                                                val = "active".equals(val) ? "1" : "0";
                                            }
                                            logger.debug("read itemDataId " + itemDataId + " value:" + val);

                                            // 如果有 http 服务接收中转，则调用 api 入库
                                            ItemData itemData = new ItemData();
                                            itemData.setVal(val);
                                            itemData.setId(Long.valueOf(itemDataId));
                                            itemData.setUpdatedAt(new Date());
                                            itemData.setCalc(1);  // 标记入库时需要通过 funcList 计算
                                            logger.debug("itemData ready " + JSONUtil.toJsonStr(itemData));

                                            if (dataServerConfig.isEnabled()) {
                                                // 生成 array
                                                if(dataServerConfig.isSendPacks()) {
                                                    packDataList.add(itemData);
                                                } else {
                                                    logger.info("sendPostAPI data r: " + dataServerConfig.getUrl() + " " + JSONUtil.toJsonStr(itemData));
                                                    sendPostAPI(dataServerConfig.getUrl(), JSONUtil.toJsonStr(itemData));
                                                }
                                            } else {
                                                // 直接入库操作
                                                logger.info("itemData update r " + JSONUtil.toJsonStr(itemData));
                                                itemDataService.updateItemData(itemData);
                                            }
                                        } catch (PropertyValueException e) {
                                            logger.warn("[read point] get value error PropertyValueException. deviceId: " + deviceId + "  oid: " + JSONUtil.toJsonStr(oid) + " oidMap: " + JSONUtil.toJsonStr(oidMap));
                                        } catch (Exception e) {
                                            logger.warn("[read point] get value error. deviceId: " + deviceId + "  oid: " + JSONUtil.toJsonStr(oid) + " oidMap: " + JSONUtil.toJsonStr(oidMap) + " error " + e.getMessage());
                                        }
                                    }
                                    // 这里一次性上传数据
                                    if (dataServerConfig.isEnabled() && dataServerConfig.isSendPacks() && packDataList.size() > 0) {
                                        logger.info("sendPostAPI data r array, deviceId: " + deviceId + " packNo: " + packNo + " colList size: " + colList.size() + " data size: " + packDataList.size());
                                        sendPostAPI(dataServerConfig.getUrl(), JSONUtil.toJsonStr(packDataList));
                                    }
                                }
                                else {
                                    logger.warn("[read point] r res pvs2 is null. deviceId " + rd.getObjectIdentifier().getInstanceNumber());
                                }
                            } catch (TimeoutException e) {
                                logger.warn("[read point] r res pvs2 error, Timeout. timeoutCount " + timeoutCount + " realTimeoutCount " + realTimeoutCount + " deviceId " + deviceId + " oids: " + JSONUtil.toJsonStr(oids));
                                timeoutCount += 1;
                                realTimeoutCount += 1;
                            } catch (InterruptedException | ExecutionException e) {
                                Thread.currentThread().interrupt(); // 保留中断状态
                                System.out.println("[read point] res pvs2 error was interrupted or failed: " + e.getMessage());
                            } catch (Exception e) {
                                // 可能包含 Connection refused: connect，只要有错误，全部当做超时错误
                                logger.warn("[read point] r res pvs2 error, Exception Error: " + e.getMessage() + ". timeoutCount " + timeoutCount + " realTimeoutCount " + realTimeoutCount + " deviceId " + deviceId + " oids: " + JSONUtil.toJsonStr(oids));
                                timeoutCount += 1;
                                realTimeoutCount += 1;
                            } finally {
                                // 确保任务取消（即使已经完成）
                                future.cancel(true);
                            }
                            // 如果错误次数过多，跳出循环，重连服务
                            if(realTimeoutCount >= bacnetConfig.getMaxTimeoutRestartCount() || (null != deviceDatas.keySet() && deviceDatas.keySet().size() > 10 && realTimeoutCount > deviceDatas.keySet().size() / 2)) {
                                logger.warn("[read point] r in needRestart flag -> true. ");
                                needRestart = true;
                                break;
                            }
                            // 如果超时次数大于最大值，休息几秒钟
                            if(timeoutCount >= maxTimeoutCount) {
                                timeoutCount = 0l;
                                Thread.sleep(timeoutSleep);
                            }
                        } catch (Exception e) {
                            logger.warn("readValues error. deviceId:" + deviceId + " oids: " + JSONUtil.toJsonStr(oids) + " error: " + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                    // 如果错误次数过多，跳出循环，重连服务
                    if(realTimeoutCount >= bacnetConfig.getMaxTimeoutRestartCount() || (null != deviceDatas.keySet() && deviceDatas.keySet().size() > 10 && realTimeoutCount > deviceDatas.keySet().size() / 2)) {
                        logger.warn("[read point] r needRestart flag -> true. ");
                        needRestart = true;
                        break;
                    }
                } else {
                    logger.debug("readValues in deviceDatas, deviceId: " + deviceId + " oidList size == 0 ");
                }
                // 读几次设备后，插入一次写指令
                if(readNum % writeInReadDevice == 0) {
                    logger.debug("readValues insert writeValue. readNum: " + readNum);
                    writeValues();
                }
            }
        }
        logger.debug("readValues finished.");
    }

    // 下发指令
    public void writeValues() {
        logger.debug("writeValues start. ");
        if(!bacnetConfig.isEnabledWrite()) {
            logger.debug("writeValues end. bacnetConfig is disabled write ");
            return ;
        }
        if(null != localDevice) {
            if (null != collectorIdList && collectorIdList.size() > 0) {
                List<Map> dataList = itemDataService.getIssueDataList(collectorIdList, "w", "N");
                if(null != dataList && dataList.size() > 0) {
                    for(Map d : dataList) {
                        try {
                            String deviceIdStr = d.get("deviceId").toString();
                            Integer deviceId = StringUtils.isNotEmpty(deviceIdStr) ? Integer.valueOf(deviceIdStr) : null;
                            if(null == deviceId || !remoteDeviceIdList.contains(deviceId)) {
                                logger.warn("writeValues error. RemoteDevice not online." + deviceIdStr);
                                continue;
                            }
                            RemoteDevice rd = remoteDevices.get(deviceId);
                            if(null == rd) {
                                logger.warn("writeValues error. no RemoteDevice find." + d.get("deviceId"));
                                continue;
                            }
                            String itemDataId = d.get("item_data_id").toString();
                            String funcCode = d.get("func").toString();
                            Integer priority = Integer.valueOf(d.get("dataGroup").toString());
                            Integer instanceNumber = Integer.valueOf(d.get("addr").toString());
                            String value = d.get("val").toString();
                            String realVal = d.get("val").toString();
                            String locked = d.get("locked").toString();
                            // 调用接口获取实际数据
                            if(StringUtils.isNotEmpty(d.get("funcList").toString())) {
                                realVal = sendPostAPI(dataServerConfig.getUrl().replace("/data", "/calcVal"), JSONUtil.toJsonStr(d));
                                if(!StringUtils.isNotEmpty(realVal)) {
                                    realVal = value;
                                }
                            }
                            // 先读取数据
                            ObjectIdentifier oid = new ObjectIdentifier(objectType(funcCode), instanceNumber);
                            logger.info("writeValues [read point] before oid:" + JSONUtil.toJsonStr(oid));
                            CompletableFuture<PropertyValues> future = CompletableFuture.supplyAsync(() -> {
                                try {
                                    PropertyValues pvs2 = RequestUtils.readOidPresentValues(localDevice, rd,
                                            Arrays.asList(oid),null);
                                    return pvs2;
                                } catch (Exception e) {
//                                    e.printStackTrace();
                                    return null;
                                }
                            });
                            try {
                                // 设置超时时间
                                PropertyValues pvs2 = future.get(bacnetConfig.getCmdTimeout(), TimeUnit.MILLISECONDS );
                                if(null != pvs2) {
                                    Encodable initValue = pvs2.get(oid, PropertyIdentifier.presentValue);
                                    logger.debug("writeValues [read point] oid:" + oid.getInstanceNumber() + " value:" + initValue
                                            + "  " + oid.getObjectType() + "_" + oid.getInstanceNumber());
                                    logger.debug("[read point] initValue:" + initValue.toString());

                                    // 更新数据需要将 outOfService 设置成 true, 否则无法 write 值
//                                RequestUtils.writeProperty(localDevice, rd,
//                                        new ObjectIdentifier(objectType(funcCode), instanceNumber),
//                                        PropertyIdentifier.outOfService,
//                                        Boolean.TRUE);
                                    logger.debug("[read point] outOfService pass sended");
                                    if(null == priority || priority > 15 || priority < 0) {
                                        RequestUtils.writePresentValue(localDevice, rd,
                                                new ObjectIdentifier(objectType(funcCode), instanceNumber),
                                                getWriteValueWithRightType(initValue, realVal));
                                    } else {
                                        RequestUtils.writeProperty(localDevice, rd,
                                                new ObjectIdentifier(objectType(funcCode), instanceNumber),
                                                PropertyIdentifier.presentValue,
                                                getWriteValueWithRightType(initValue, realVal), priority);
                                    }
                                    logger.info("【writeValues success】. success send to device. itemDataId: " + itemDataId + " value: " + value + " realVal:" + realVal);
                                    // 更新 w 点
                                    ItemData itemData = new ItemData();
                                    itemData.setId(Long.valueOf(itemDataId));
                                    itemData.setUpdatedAt(new Date());
                                    itemData.setVal(value);
                                    if(!"1".equals(locked)) {
                                        itemData.setHasSync("Y");
                                    }
                                    itemData.setCalc(1);  // 标记入库时需要通过 funcList 计算
                                    if (dataServerConfig.isEnabled()) {
                                        logger.info("sendPostAPI data w: " + dataServerConfig.getUrl() + " " + JSONUtil.toJsonStr(itemData));
                                        sendPostAPI(dataServerConfig.getUrl(), JSONUtil.toJsonStr(itemData));
                                    } else {
                                        logger.info("itemData update w " + JSONUtil.toJsonStr(itemData));
                                        itemDataService.updateItemData(itemData);
                                    }
                                    // 更新 r 点
                                    // itemData.setId(Long.valueOf(itemDataId) - 1);
                                    // itemData.setVal(value);
                                    // itemData.setUpdatedAt(new Date());
                                    // itemDataService.updateItemData(itemData);
                                }
                                else {
                                    logger.warn("[read point] w res pvs2 is null. deviceId " + rd.getObjectIdentifier().getInstanceNumber());
                                }
                            } catch (TimeoutException e) {
                                logger.warn("[read point] w error, Timeout. deviceId " + deviceId + " funcCode " + funcCode + " instanceNum " + instanceNumber);
                            } catch (InterruptedException | ExecutionException e) {
                                Thread.currentThread().interrupt(); // 保留中断状态
                                System.out.println("[read point] w error was interrupted or failed: " + e.getMessage());
                            } finally {
                                // 确保任务取消（即使已经完成）
                                future.cancel(true);
                            }
                        } catch (BACnetTimeoutException et) {
                            logger.warn("writeValues timeout. data:" + JSONUtil.toJsonStr(d));
                        } catch (Exception e) {
                            logger.warn("writeValues error. data:" + JSONUtil.toJsonStr(d) + " error: " + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                }
            } else {
                logger.debug("writeValues collectorIdList has not contain a valid collector ID");
            }
        }
        logger.debug("writeValues finished.");
    }

    public List<ObjectIdentifier> getDeviceOids(Integer deviceId) {
        List<ObjectIdentifier> oids = new ArrayList();
        try {
            RemoteDevice rd = getRemoteDevice(deviceId);
            List<ObjectIdentifier> objectList = RequestUtils.getObjectList(localDevice, rd).getValues();
            for (ObjectIdentifier o : objectList) {
                if(o.getObjectType().toString().contains("analog-value")) {
                    oids.add(new ObjectIdentifier(ObjectType.analogValue, o.getInstanceNumber()));
                } else if(o.getObjectType().toString().contains("analog-input")) {
                    oids.add(new ObjectIdentifier(ObjectType.analogInput, o.getInstanceNumber()));
                } else if(o.getObjectType().toString().contains("analog-output")) {
                    oids.add(new ObjectIdentifier(ObjectType.analogOutput, o.getInstanceNumber()));
                } else if (o.getObjectType().toString().contains("binary-value")) {
                    oids.add(new ObjectIdentifier(ObjectType.binaryValue, o.getInstanceNumber()));
                } else if (o.getObjectType().toString().contains("binary-input")) {
                    oids.add(new ObjectIdentifier(ObjectType.binaryInput, o.getInstanceNumber()));
                } else if (o.getObjectType().toString().contains("binary-output")) {
                    oids.add(new ObjectIdentifier(ObjectType.binaryOutput, o.getInstanceNumber()));
                } else if(o.getObjectType().toString().contains("characterstring-value")) {
                    oids.add(new ObjectIdentifier(ObjectType.characterstringValue, o.getInstanceNumber()));
                } else if (o.getObjectType().toString().contains("multi-state-value")) {
                    oids.add(new ObjectIdentifier(ObjectType.multiStateValue, o.getInstanceNumber()));
                } else if (o.getObjectType().toString().contains("multi-state-input")) {
                    oids.add(new ObjectIdentifier(ObjectType.multiStateInput, o.getInstanceNumber()));
                } else if (o.getObjectType().toString().contains("multi-state-output")) {
                    oids.add(new ObjectIdentifier(ObjectType.multiStateOutput, o.getInstanceNumber()));
                } else if (o.getObjectType().toString().contains("bitstring-value")) {
                    oids.add(new ObjectIdentifier(ObjectType.bitstringValue, o.getInstanceNumber()));
                }
            }
        } catch (Exception e) {
            logger.warn("getDeviceOids error. deviceId: " + deviceId + " err: " + e.getMessage());
        }
        return oids;
    }

    public ObjectType objectType(String checkFunc) {
        if("analog-value".equals(checkFunc)) {
            return ObjectType.analogValue;
        } else if("analog-input".equals(checkFunc)) {
            return ObjectType.analogInput;
        } else if("analog-output".equals(checkFunc)) {
            return ObjectType.analogOutput;
        } else if("binary-value".equals(checkFunc)) {
            return ObjectType.binaryValue;
        } else if("binary-input".equals(checkFunc)) {
            return ObjectType.binaryInput;
        } else if("binary-output".equals(checkFunc)) {
            return ObjectType.binaryOutput;
        } else if("characterstring-value".equals(checkFunc)) {
            return ObjectType.characterstringValue;
        } else if("multi-state-value".equals(checkFunc)) {
            return ObjectType.multiStateValue;
        } else if("multi-state-input".equals(checkFunc)) {
            return ObjectType.multiStateInput;
        } else if("multi-state-output".equals(checkFunc)) {
            return ObjectType.multiStateOutput;
        } else if("bitstring-value".equals(checkFunc)) {
            return ObjectType.bitstringValue;
        }
        return null;
    }

    private Encodable getWriteValueWithRightType(Encodable initValue, String value) {
        if (initValue instanceof Real) {
            logger.debug("getWriteValueWithRightType instanceof Real " + initValue + " val:" + value);
            return new Real(Float.parseFloat(value));
        }
        if (initValue instanceof Double) {
            logger.debug("getWriteValueWithRightType instanceof Double" + initValue + " val:" + value);
            return new Double(java.lang.Double.parseDouble(value));
        }
        if (initValue instanceof OctetString) {
            logger.debug("getWriteValueWithRightType instanceof OctetString" + initValue + " val:" + value);
            return new OctetString(value.getBytes());
        }
        if (initValue instanceof SignedInteger) {
            logger.debug("getWriteValueWithRightType instanceof SignedInteger" + initValue + " val:" + value);
            return new SignedInteger(Integer.parseInt(value));
        }
        if (initValue instanceof UnsignedInteger) {
            logger.debug("getWriteValueWithRightType instanceof UnsignedInteger" + initValue + " val:" + value);
            return new UnsignedInteger(Integer.parseInt(value));
        }
        if (initValue instanceof Enumerated) {
            // com.serotonin.bacnet4j.type.enumerated.BinaryPV
            logger.debug("getWriteValueWithRightType instanceof Enumerated" + initValue + " val:" + value);
            return new Enumerated(Integer.parseInt(value));
//            return new Enumerated("active".equals(value) ? 1 : 0);
        }
        if (initValue instanceof Boolean) {
            logger.debug("getWriteValueWithRightType instanceof Boolean" + initValue + " val:" + value);
            return null;
//            return new com.serotonin.bacnet4j.type.primitive.Boolean("1".equals(value));
        }
        logger.debug("getWriteValueWithRightType instanceof not found " + initValue.getClass().getTypeName() + " val:" + value);
        return null;
    }

    public static String sendPostAPI(String url, String json) {
        String timestamp = String.valueOf(DateUtil.current());
        Map<String, String> heads = new HashMap<>();
        heads.put("Content-Type", "application/json;charset=UTF-8");
        //heads.put("auth-cert-timestamp", timestamp);
        return HttpRequest.post(url)
                //.headerMap(heads, false)
                .body(json)
                .execute().body();
    }
}
