package com.ruoyi.bacnet.service;

import cn.hutool.json.JSONUtil;
import com.ruoyi.common.utils.StringUtils;
import com.serotonin.bacnet4j.event.DeviceEventAdapter;
import com.serotonin.bacnet4j.type.constructed.PropertyValue;
import com.serotonin.bacnet4j.type.constructed.SequenceOf;
import com.serotonin.bacnet4j.type.enumerated.PropertyIdentifier;
import com.serotonin.bacnet4j.type.primitive.ObjectIdentifier;
import com.serotonin.bacnet4j.type.primitive.UnsignedInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CovNotifListener extends DeviceEventAdapter {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final CovNotifListenerCallback callback;

    public CovNotifListener(CovNotifListenerCallback callback) {
        this.callback = callback;
    }

    @Override
    public void covNotificationReceived(final UnsignedInteger subscriberProcessIdentifier,
                                        final ObjectIdentifier initiatingDevice, final ObjectIdentifier monitoredObjectIdentifier,
                                        final UnsignedInteger timeRemaining, final SequenceOf<PropertyValue> listOfValues) {
        logger.debug("covNotificationReceived. " + subscriberProcessIdentifier);
        logger.debug(JSONUtil.toJsonStr(listOfValues));
        String deviceId = String.valueOf(initiatingDevice.getInstanceNumber());
        // 遍历 listOfValues 获取每个 PropertyValue 的详细信息
        String func = "";
        String instance = "";
        String value = "";
        try {
            for (PropertyValue propertyValue : listOfValues) {
                // 获取属性标识符
                PropertyIdentifier propertyIdentifier = propertyValue.getPropertyIdentifier();
                if (propertyIdentifier.equals(PropertyIdentifier.presentValue)) {
                    instance = String.valueOf(monitoredObjectIdentifier.getInstanceNumber());
                    func = monitoredObjectIdentifier.getObjectType().toString();
                    value = propertyValue.getValue().toString();
                    // 针对 binary 数据，转换成 0 和 1
                    if (func.contains("binary")) {
                        value = "active".equals(value) ? "1" : "0";
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("covNotificationReceived error " + e.getMessage());
        }
        if(StringUtils.isNotEmpty(func) && StringUtils.isNotEmpty(instance) && null != callback) {
            callback.process(String.valueOf(deviceId), func, instance, value);
        }
    }
}
