package com.ruoyi.bacnet.task;

import com.ruoyi.bacnet.service.BacnetService;
import com.ruoyi.common.config.BacnetConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class BacnetTask {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BacnetConfig bacnetConfig;

    @Autowired
    private BacnetService bacnetService;

    // 每次跑完休息1秒
    @Scheduled(fixedDelay= 200)
    public void collectBacnetTask() {
        if (!bacnetConfig.isEnabledJob()) {
            logger.debug("resetBacnetTask task enabledJob false");
            return;
        }
        // 采集服务运行
        bacnetService.runing();
    }
}
