package com.ruoyi.bacnet.utils;

import com.serotonin.bacnet4j.LocalDevice;
import com.serotonin.bacnet4j.RemoteDevice;
import com.serotonin.bacnet4j.exception.BACnetException;
import com.serotonin.bacnet4j.npdu.ip.IpNetwork;
import com.serotonin.bacnet4j.npdu.ip.IpNetworkBuilder;
import com.serotonin.bacnet4j.service.acknowledgement.ReadPropertyAck;
import com.serotonin.bacnet4j.service.confirmed.ReadPropertyRequest;
import com.serotonin.bacnet4j.transport.DefaultTransport;
import com.serotonin.bacnet4j.transport.Transport;
import com.serotonin.bacnet4j.type.Encodable;
import com.serotonin.bacnet4j.type.enumerated.PropertyIdentifier;
import com.serotonin.bacnet4j.type.primitive.ObjectIdentifier;
import com.serotonin.bacnet4j.util.DiscoveryUtils;
import com.serotonin.bacnet4j.util.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public class BACnetUtil {

    protected static Logger logger = LoggerFactory.getLogger(BACnetUtil.class);

    public static LocalDevice initLocalDevice(IpNetwork ipNetwork, int localDeviceNumber) throws Exception {
        logger.debug("************************** 初始化本地虚拟设备 " + localDeviceNumber + " ****************************");
        LocalDevice localDevice = new LocalDevice(localDeviceNumber, new DefaultTransport(ipNetwork));
        localDevice.initialize();
        localDevice.startRemoteDeviceDiscovery();
        return localDevice;
    }

    public static IpNetwork initIpNetwork(String ip, String subnet, Integer port) {
        logger.debug("************************** 初始化网络信息 ****************************");
        IpNetworkBuilder ipNetworkBuilder = new IpNetworkBuilder()
                .withLocalBindAddress(ip)
                .withSubnet(subnet, 24)
                .withPort(port)
                .withBroadcast(IpUtil.getBroadcastByIpAndSubnet(ip, subnet), 24)
                .withReuseAddress(true);

        logger.debug("ip:        " + ip);
        logger.debug("subnet:    " + subnet);
        logger.debug("broadcast: " + IpUtil.getBroadcastByIpAndSubnet(ip, subnet));
        String networkSegment = IpUtil.getNetworkSegmentByIpAndSubnet(ip, subnet);
        logger.debug("所在网段:    " + networkSegment);

        IpNetwork ipNetwork = ipNetworkBuilder.build();
        ipNetwork.enableBBMD();
        return ipNetwork;
    }
}

