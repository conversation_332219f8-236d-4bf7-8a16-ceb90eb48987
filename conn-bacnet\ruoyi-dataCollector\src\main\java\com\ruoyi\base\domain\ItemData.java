package com.ruoyi.base.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 设备实时数据对象 a_item_data
 *
 * <AUTHOR>
 * @date 2020-10-29
 */
public class ItemData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long itemId;

    /** 数据名 */
    @Excel(name = "数据名")
    private String name;

    /** 数据内容 */
    @Excel(name = "数据内容")
    private String val;

    /** 数据类型 */
    @Excel(name = "数据类型")
    private String dataType;

    /** 数据单位 */
    @Excel(name = "数据单位")
    private String dataUnit;

    /** 倍率系数 */
    @Excel(name = "倍率系数")
    private BigDecimal coefficient;

    /** 备注 */
    @Excel(name = "备注")
    private String note;

    /** 读写标识 */
    @Excel(name = "读写标识")
    private String func;

    /** 是否同步 */
    @Excel(name = "是否同步")
    private String hasSync;

    /** 是否需要funcList计算 */
    @Excel(name = "是否需要funcList计算")
    private Integer calc;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setVal(String val)
    {
        this.val = val;
    }

    public String getVal()
    {
        return val;
    }

    public void setDataType(String dataType)
    {
        this.dataType = dataType;
    }
    public String getDataType()
    {
        return dataType;
    }

    public void setDataUnit(String dataUnit)
    {
        this.dataUnit = dataUnit;
    }

    public String getDataUnit()
    {
        return dataUnit;
    }
    public void setCoefficient(BigDecimal coefficient)
    {
        this.coefficient = coefficient;
    }

    public BigDecimal getCoefficient()
    {
        return coefficient;
    }
    public void setNote(String note)
    {
        this.note = note;
    }

    public String getNote()
    {
        return note;
    }
    public void setFunc(String func)
    {
        this.func = func;
    }

    public String getFunc()
    {
        return func;
    }
    public void setHasSync(String hasSync)
    {
        this.hasSync = hasSync;
    }

    public String getHasSync()
    {
        return hasSync;
    }

    public Integer getCalc() { return this.calc; }
    public void setCalc(Integer calc) { this.calc = calc; }

    public void setCreatedAt(Date createdAt)
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt()
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt)
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt()
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("itemId", getItemId())
                .append("name", getName())
                .append("val", getVal())
                .append("dataType", getDataType())
                .append("dataUnit", getDataUnit())
                .append("coefficient", getCoefficient())
                .append("note", getNote())
                .append("func", getFunc())
                .append("hasSync", getHasSync())
                .append("createdAt", getCreatedAt())
                .append("updatedAt", getUpdatedAt())
                .toString();
    }
}
