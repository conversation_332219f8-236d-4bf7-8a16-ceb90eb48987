package com.ruoyi.base.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.base.domain.ItemData;
import org.apache.ibatis.annotations.Param;

/**
 * 设备实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2020-10-29
 */
public interface ItemDataMapper
{
    /**
     * 查询设备实时数据
     *
     * @param id 设备实时数据ID
     * @return 设备实时数据
     */
    public ItemData selectItemDataById(Long id);

    /**
     * 查询设备实时数据列表
     *
     * @param itemData 设备实时数据
     * @return 设备实时数据集合
     */
    public List<ItemData> selectItemDataList(ItemData itemData);

    /**
     * 新增设备实时数据
     *
     * @param itemData 设备实时数据
     * @return 结果
     */
    public int insertItemData(ItemData itemData);

    /**
     * 修改设备实时数据
     *
     * @param itemData 设备实时数据
     * @return 结果
     */
    public int updateItemData(ItemData itemData);

    /**
     * 删除设备实时数据
     *
     * @param id 设备实时数据ID
     * @return 结果
     */
    public int deleteItemDataById(Long id);

    /**
     * 批量删除设备实时数据
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteItemDataByIds(Long[] ids);


    public List<Map> itemDataList(@Param("collectorId") Long collectorId);

    List<Map> getIssueDataList(@Param("collectorIds") List<String> collectorIds,
                               @Param("type") String type,
                               @Param("hasSync")String hasSync);
}
