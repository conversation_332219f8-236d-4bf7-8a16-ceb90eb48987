package com.ruoyi.base.service;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.base.mapper.ItemDataMapper;
import com.ruoyi.base.domain.ItemData;

/**
 * 设备实时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-10-29
 */
@Service
public class ItemDataService {
    @Autowired
    private ItemDataMapper itemDataMapper;

    /**
     * 查询设备实时数据
     *
     * @param id 设备实时数据ID
     * @return 设备实时数据
     */
    public ItemData selectItemDataById(Long id)
    {
        return itemDataMapper.selectItemDataById(id);
    }

    /**
     * 查询设备实时数据列表
     *
     * @param itemData 设备实时数据
     * @return 设备实时数据
     */
    public List<ItemData> selectItemDataList(ItemData itemData)
    {
        return itemDataMapper.selectItemDataList(itemData);
    }

    /**
     * 新增设备实时数据
     *
     * @param itemData 设备实时数据
     * @return 结果
     */
    public int insertItemData(ItemData itemData)
    {
        return itemDataMapper.insertItemData(itemData);
    }

    /**
     * 修改设备实时数据
     *
     * @param itemData 设备实时数据
     * @return 结果
     */
    public int updateItemData(ItemData itemData)
    {
        return itemDataMapper.updateItemData(itemData);
    }

    /**
     * 批量删除设备实时数据
     *
     * @param ids 需要删除的设备实时数据ID
     * @return 结果
     */
    public int deleteItemDataByIds(Long[] ids)
    {
        return itemDataMapper.deleteItemDataByIds(ids);
    }

    /**
     * 删除设备实时数据信息
     *
     * @param id 设备实时数据ID
     * @return 结果
     */
    public int deleteItemDataById(Long id)
    {
        return itemDataMapper.deleteItemDataById(id);
    }


    public List<Map> itemDataList(Long collectorId) {
        return itemDataMapper.itemDataList(collectorId);
    }

    public List<Map> getIssueDataList(List<String> collectorIdList, String type, String hasSync) {
        return itemDataMapper.getIssueDataList(collectorIdList, type, hasSync);
    }
}
