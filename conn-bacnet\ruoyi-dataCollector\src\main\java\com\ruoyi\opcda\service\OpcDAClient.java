package com.ruoyi.opcda.service;

import lombok.extern.slf4j.Slf4j;
import org.jinterop.dcom.common.JIErrorCodes;
import org.jinterop.dcom.common.JIException;
import org.jinterop.dcom.core.JIVariant;
import org.openscada.opc.dcom.da.OPCSERVERSTATE;
import org.openscada.opc.lib.common.AlreadyConnectedException;
import org.openscada.opc.lib.common.ConnectionInformation;
import org.openscada.opc.lib.common.NotConnectedException;
import org.openscada.opc.lib.da.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.UnknownHostException;
import java.util.*;


public class OpcDAClient {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private String host;

    private String user;

    private String password;

    private String clsId;

    private Server server;

    private Integer groupCount;

    private Group group = null;

    private Map<String, Item> groupItems = null;

    /**
     * 初始化连接信息
     * @param host
     * @param user
     * @param password
     * @param clsId
     */
    public OpcDAClient(String host, String user, String password, String clsId, Integer groupCount) {
        this.host = host;
        this.user = user;
        this.password = password;
        this.clsId = clsId;
        this.groupCount = groupCount;
    }

    /**
     * 创建连接
     */
    public void connect() {
        if(server.getServerState() != null && server.getServerState().getServerState().equals(OPCSERVERSTATE.OPC_STATUS_RUNNING)) {
            return;
        }
        final ConnectionInformation ci = new ConnectionInformation();
        ci.setHost(host);
        ci.setDomain(""); // 域 为空
        ci.setUser(user);
        ci.setPassword(password);

        ci.setClsid(clsId);
        server = new Server(ci, null);
        try {
            server.connect();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            logger.error("opc host error: " + e.getCause() + " " + e.getMessage());
        } catch (JIException e) {
            e.printStackTrace();
            logger.error("opc connect failed: " + e.getCause() + " " + e.getMessage());
        } catch (AlreadyConnectedException e) {
            e.printStackTrace();
            logger.error("opc has connected: ", e.getCause() + " " + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("opc connect error: ", e.getCause() + " " + e.getMessage());
        }
        logger.info("OPC Server connect success.");
    }

    /**
     * 根据地址获取数据
     * @param itemId
     * @return
     */
    public Object getItemValue(String itemId) {
        try {
            Group group = server.addGroup();
            Item item = group.addItem(itemId);
            return getVal(item.read(true).getValue());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("getItemValue error itemId: " + itemId + " " + e.getCause() + " " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取多组数据
     * @param itemIdList
     * @return
     */
    public Map<String, Object> getItemValues(String groupName, List<String> itemIdList) {
        Map<String, Object> result = new HashMap<>();
        try {
            Group group = server.findGroup(groupName);
            String[] items = itemIdList.toArray(new String[]{});
            groupItems = group.addItems(items);

            Set itemSet = new HashSet(groupItems.values());
            Item[] itemArr = new Item[itemSet.size()];
            itemSet.toArray(itemArr);
            Map<Item, ItemState> resultMap = group.read(true, itemArr);
            logger.info("数据获取完成：{}条", resultMap.size());
            for(Item item : resultMap.keySet()) {
                ItemState itemMap = resultMap.get(item);
                result.put(item.getId(), getVal(itemMap.getValue()));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("批量获取数据异常：", e);
        }
        return result;
    }

    /**
     * 获取value
     * @param var
     * @return
     * @throws JIException
     */
    private static Object getVal(JIVariant var) throws JIException {
        Object value;
        int type = var.getType();
        switch (type) {
            case JIVariant.VT_I2:
                value = var.getObjectAsShort();
                break;
            case JIVariant.VT_I4:
                value = var.getObjectAsInt();
                break;
            case JIVariant.VT_I8:
                value = var.getObjectAsLong();
                break;
            case JIVariant.VT_R4:
                value = var.getObjectAsFloat();
                break;
            case JIVariant.VT_R8:
                value = var.getObjectAsDouble();
                break;
            case JIVariant.VT_BSTR:
                value = var.getObjectAsString2();
                break;
            case JIVariant.VT_BOOL:
                value = var.getObjectAsBoolean();
                break;
            case JIVariant.VT_UI2:
            case JIVariant.VT_UI4:
                value = var.getObjectAsUnsigned().getValue();
                break;
            case JIVariant.VT_EMPTY:
                throw new JIException(JIErrorCodes.JI_VARIANT_IS_NULL, "Variant is Empty.");
            case JIVariant.VT_NULL:
                throw new JIException(JIErrorCodes.JI_VARIANT_IS_NULL, "Variant is null.");
            default:
                throw new JIException(JIErrorCodes.JI_VARIANT_IS_NULL, "Unknown Type.");
        }

        return value;
    }

    /**
     * 数据分组, 每组n个
     * @param source 要分组的数据源
     * @param n      每组n个
     */
    public static <T> List<List<T>> packList(List<T> source, int n) {
        List<List<T>> result = new ArrayList<List<T>>();
        int remainder = source.size() % n;  //(先计算出余数)
        int number = source.size() / n;  //然后是商
        for (int i = 0; i < (remainder > 0 ? (number + 1) : number); i++) {
            List<T> value = null;
            value = source.subList((i-1) * number + n, Math.min(source.size(), i * number + n + 1));
            result.add(value);
        }
        return result;
    }

    /**
     * 关闭连接
     */
    public void disconnect() {
        if(server != null) {
            server.disconnect();
        }
        if (null == server.getServerState()) {
            logger.info("OPC Server Disconnect.");
        }
    }

}