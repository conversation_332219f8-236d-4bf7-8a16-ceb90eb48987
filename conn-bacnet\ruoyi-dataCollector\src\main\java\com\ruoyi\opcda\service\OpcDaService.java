package com.ruoyi.opcda.service;

import cn.hutool.json.JSONUtil;
import com.ruoyi.base.service.ItemDataService;
import com.ruoyi.common.config.OpcDaConfig;
import com.ruoyi.common.utils.StringUtils;
import org.openscada.opc.lib.da.Server;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class OpcDaService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OpcDaConfig opcDaConfig;

    @Autowired
    private ItemDataService itemDataService;

    // collectIdList
    private List<String> collectorIdList;
    private List<Map> itemDataList = new ArrayList<>();

    private OpcDAClient opcDAClient;

    public void runing() throws Exception {
        // 需要这里打印日志，触发延迟加载或初始化操作，否则会被更下面的同名配置覆盖掉
        logger.debug("opcDaConfig " + JSONUtil.toJsonStr(opcDaConfig));
        if(opcDaConfig.isEnabledJob()) {
            // 创建异步任务，超时重启采集
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                initCollectData();
                startCollect();
                return "initCollectData and startCollect finished.";
            });
            try {
                // 设置超时时间
                String res = future.get(opcDaConfig.getCollectTimeout(), TimeUnit.MILLISECONDS );
                logger.warn("runing success. " + res);
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("runing error. " + e.getMessage() + " now resetServer.");
                closeClient();
            }
        } else {
            logger.debug("OpcDaService enabledJob false");
        }


    }

    // 载入初始化采集数据
    public void initCollectData() {
        logger.debug("initCollectData start. ");
        // 准备 采集器列表
        String collectorIds = opcDaConfig.getCollectorIds();
        if(null != collectorIds && StringUtils.isNotEmpty(collectorIds)) {
            collectorIdList = Arrays.asList(collectorIds.split(","));
        }
        // 载入所有数据点
        if(null != collectorIdList && collectorIdList.size() > 0) {
            itemDataList = new ArrayList<>();
            for(String id: collectorIdList) {
                if(StringUtils.isNotEmpty(id)) {
                    List<Map> dataList = itemDataService.itemDataList(Long.valueOf(id));
                    itemDataList.addAll(dataList);
                }
            }
        }
        logger.debug("initCollectData end. ");
    }

    public void startCollect() {
        try {
            // 连接服务
            if(null == opcDAClient) {
                opcDAClient = new OpcDAClient(opcDaConfig.getServer(), opcDaConfig.getUser(), opcDaConfig.getPassword(), opcDaConfig.getClsid(), 5);
                opcDAClient.connect();
            }
            // 根据数据建好分组
            List<String> itemIds = itemDataList.stream().map(item -> item.get("func").toString()).collect(Collectors.toList());
            List<List<String>> packList = opcDAClient.packList(itemIds, opcDaConfig.getPackSize());
            for (int i = 0; i < packList.size(); i++) {
                String groupName = String.valueOf(i);
                List<String> innerList = packList.get(i);
                Map<String, Object> itemValues = opcDAClient.getItemValues(groupName, innerList);

            }
        } catch (Exception e) {
            // pass
        }
    }

    // 关闭连接
    public void closeClient() {
        try {
            if(null != opcDAClient) {
                opcDAClient.disconnect();
            }
        } catch (Exception e) {
            logger.warn("closeClient error: " + e.getMessage());
        }
        opcDAClient = null;
    }
}
