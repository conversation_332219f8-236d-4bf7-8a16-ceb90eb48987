package com.ruoyi.opcda.task;

import com.ruoyi.common.config.OpcDaConfig;
import com.ruoyi.opcda.service.OpcDaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class OpcDaTask {

    @Autowired
    private OpcDaConfig opcDaConfig;
    @Autowired
    private OpcDaService opcDaService;

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    // 每次跑完休息1秒
    @Scheduled(fixedDelay= 500)
    public void collectOpcDaTask() {
        if (!opcDaConfig.isEnabledJob()) {
            return;
        }
        // 采集服务运行
        try {
            opcDaService.runing();
        } catch (Exception e) {
            logger.warn("collectOpcDaTask error." + e.getCause() + " " + e.getMessage());
        }

    }
}
