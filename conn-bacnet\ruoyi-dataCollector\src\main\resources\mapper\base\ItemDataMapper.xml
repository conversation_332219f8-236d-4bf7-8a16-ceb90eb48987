<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.ItemDataMapper">

    <resultMap type="ItemData" id="ItemDataResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="name"    column="name"    />
        <result property="val"    column="val"    />
        <result property="dataType"    column="data_type"    />
        <result property="dataUnit"    column="data_unit"    />
        <result property="coefficient"    column="coefficient"    />
        <result property="note"    column="note"    />
        <result property="func"    column="func"    />
        <result property="hasSync"    column="has_sync"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectItemDataVo">
        select id, item_id, name, val, data_type, data_unit, coefficient, note, func, has_sync, created_at, updated_at from a_item_data
    </sql>

    <select id="selectItemDataList" parameterType="ItemData" resultMap="ItemDataResult">
        <include refid="selectItemDataVo"/>
        <where>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="val != null  and val != ''"> and val = #{val}</if>
            <if test="dataType != null "> and data_type = #{dataType}</if>
            <if test="dataUnit != null  and dataUnit != ''"> and data_unit = #{dataUnit}</if>
            <if test="coefficient != null "> and coefficient = #{coefficient}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
            <if test="func != null  and func != ''"> and func = #{func}</if>
            <if test="hasSync != null  and hasSync != ''"> and has_sync = #{hasSync}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
        order by item_id, updated_at desc
    </select>

    <select id="selectItemDataById" parameterType="Long" resultMap="ItemDataResult">
        <include refid="selectItemDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertItemData" parameterType="ItemData" useGeneratedKeys="true" keyProperty="id">
        insert into a_item_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="val != null and val != ''">val,</if>
            <if test="dataType != null">data_type,</if>
            <if test="dataUnit != null">data_unit,</if>
            <if test="coefficient != null">coefficient,</if>
            <if test="note != null">note,</if>
            <if test="func != null">func,</if>
            <if test="hasSync != null">has_sync,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="val != null and val != ''">#{val},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="dataUnit != null">#{dataUnit},</if>
            <if test="coefficient != null">#{coefficient},</if>
            <if test="note != null">#{note},</if>
            <if test="func != null">#{func},</if>
            <if test="hasSync != null">#{hasSync},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
        </trim>
    </insert>

    <update id="updateItemData" parameterType="ItemData">
        update a_item_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="val != null and val != ''">val = #{val},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="dataUnit != null">data_unit = #{dataUnit},</if>
            <if test="coefficient != null">coefficient = #{coefficient},</if>
            <if test="note != null">note = #{note},</if>
            <if test="func != null">func = #{func},</if>
            <if test="hasSync != null">has_sync = #{hasSync},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteItemDataById" parameterType="Long">
        delete from a_item_data where id = #{id}
    </delete>

    <delete id="deleteItemDataByIds" parameterType="String">
        delete from a_item_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="itemDataList" resultType="java.util.Map">
        select
            d.id as itemId,
            d.name as itemName,
            d.code as itemCode,
            ifnull(d.description, '') as itemDataHandle,
            dd.id as itemDataId,
            dd.name as itemDataName,
            dd.default_val as itemDataDefaultVal,
            ifnull(dd.data_type,'') as dataType,
            dd.coefficient as coefficient,
            ifnull(p.addr, '') addr,
            ifnull(p.device_id,'') as deviceId,
            ifnull(p.func_list,'') as funcList,
            ifnull(p.length,'') as length,
            ifnull(p.func,'') as funcCode
        from a_item d
                 left join a_collector c on c.id = d.collector_id
                 left join a_item_data dd on d.id = dd.item_id
                 left join a_point_table p on p.item_data_id = dd.id and p.collector_id = c.id
        where c.id = #{collectorId}
          and p.type = 'r'
          and dd.id is not null
          and p.device_id is not null
          and p.addr is not null
          and p.func is not null
    </select>

    <select id="getIssueDataList" resultType="java.util.Map">
        select
            p.item_data_id,
            it.item_id,
            SUBSTR(it.val, 1, length(it.val)) as val,
            ifnull(it.locked,0) as `locked`,
            it.has_sync,
            ifnull(i.code,'') as itemCode,
            ifnull(p.func,'') as func,
            ifnull(p.func_list,'') as funcList,
            ifnull(p.addr,'') as addr,
            ifnull(p.device_id,'') as deviceId,
            ifnull(p.data_group, 15) as dataGroup,
            ifnull(c.note,'') as collector_code
        from a_point_table p
                 LEFT JOIN a_item_data it on it.id=p.item_data_id
                 LEFT join a_item i on it.item_id = i.id
                 LEFT join a_collector c on c.id = p.collector_id and p.collector_id = c.id
        where p.type = #{type}
            and it.has_sync = #{hasSync}
            and it.item_id is not null
            and p.collector_id in
            <foreach item="item" index="index" collection="collectorIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        order by p.addr
    </select>

</mapper>