var pzmodels = []; //配置模型的清单
var projectId;
var publicids;
var alreadymodels = []; //已添加的模型列表
var alreadylines = []; //已添加的管道列表

var projectid;
var publicToken;
var updatedData = {
  config: {
    //灯光
    isfw: false, //是否显示复位
    isshowchange: false, //是否显示分组   false  代表不显示   right代表显示右边的   bottom 代表显示下边的
    lightConfig: [
      {
        type: "AmbientLight",
        color: "#ffffff",
        intensity: 1.5,
      },
      {
        type: "DirectionalLight",
        color: "#ffffff",
        intensity: 1.54,
        position: [39.23727256258536, 49.10284184568695, -88.62454182675],
      },
    ],
    dracoPath: "",
    hdrPath: "",
    camera: {
      position: [0, 0, 0],
      target: [0, 0, 0],
      near: 1, // 近截面
      far: 3000000,
    },
    css2d: {
      use: true,
    },
    css3d: {
      use: true,
    },
    floorlist: [],
    useEffectComposer: true,
    models: [
      {
        path: "",

        name: "shebei",
        scale: [1, 1, 1],
        rotation: [0, 0, 0],
        id: 1,
        visible: true,
        groupNames: [], // 用来配置点击选中是可以选择个组，例如一栋楼，一个电机。而不是只能选中某一部件
        isGlow: true, // 是否使用辉光
        glowNames: [], //如果设置就是局部辉光，如果不设置就是整体辉光
        transparentConf: [
          //配置哪些组需要设置透明度
          {
            names: [],
            opacity: 0.38,
          },
          {
            names: ["rf_sb"],
            opacity: 1,
          },
        ],
        // industryNames: ["B3"],
      },
    ],
  },
};
// 示例 JSON 数据
var Scenecity = {};
var poijson;
// window.addEventListener("message", (event) => {
//   if (event.data.type === "3DData") {
//     // 处理3D数据
//     console.log(event.data.data.token, event.data.data.id, "vuehhh");
//     localStorage.setItem("tokens", event.data.data.token);
//     localStorage.setItem("moxingid", event.data.data.id);
//     localStorage.setItem("moids", event.data.data.ids);
//     projectId = event.data.data.id;
//     getpro(event.data.data.ids);
//     vm.getmodeltableData(0, projectId);
//     vm.getmodeltableData(1, projectId);
//   }
// });
const api = axios.create({
  baseURL: LocalUrl, // 这里可以配置你的API基础路径
  timeout: 5000, // 请求超时时间
});

api.interceptors.request.use(
  (config) => {
    // 这里添加请求前的处理，比如添加token等
    const axtoken = localStorage.getItem("tokens");
    console.log(localStorage.getItem("tokens"));
    if (axtoken) {
      config.headers.token = `${axtoken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 封装GET请求
function get(url, params) {
  return api.get(url, { params });
}

// 封装POST请求
function post(url, data) {
  return api.post(url, data);
}

var vm = new Vue({
  el: "#all",
  data() {
    return {
      pjurl: "",
      anglemodel: {
        floorId: "0",
        pzjson:
          '{"modelId":"r-K_mGF8I0FsH1ZhYaraqQ==","position":[3.274062062010605,-1.172255676961862,-1.3070314678102868],"ue_position":[327.4062062010605,-130.7031467810287,-117.22556769618619],"scale":[1,1,1],"rotation":[0,0,0],"id":3704,"floorNum":2,"name":"卧式水泵"}',
        name: "卧式水泵",
        json: "",
        buildId: "0",
        id: 501206,
        sceneJson: null,
        type: 1,
        deviceId: "48806",
        projectId: 604,
        parkId: "0",
      },
      filePaths: [],
      isshowchange: "",
      visible: true,
      modeltableData: [],
      linetableData: [],
      leftBtnIndex: null,
      rightBtnIndex: null,
      rightBtnIndex1: null,
      rightBtnIndex2: null,
      input: "",
      active: null,
      jdata: [],
      selectedMenu: null,
      submenuItems: [],
      selectedSubmenuItem: null,
      rightBtn: [],
      jdata1: [
        {
          name: "全部",
          id: "0",
        },
      ],
      sizeForm: {
        title: "管道",
        id: "",
        texturePath: "",
        direction: 0,
        type: 2,
        repeatX: 10,
        size: 0.1,
        color: "",
      },
      color: "rgba(255, 69, 0, 0.68)",
      predefineColors: [
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "rgba(255, 69, 0, 0.68)",
        "rgb(255, 120, 0)",
        "hsv(51, 100, 98)",
        "hsva(120, 40, 94, 0.5)",
        "hsl(181, 100%, 37%)",
        "hsla(209, 100%, 56%, 0.73)",
        "#c7158577",
      ],
      mlist: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
      placename: "放置模型",
      isiclose: false,
      isangclose: false,
    };
  },
  mounted() {
    var that = this;
    this.pjurl = pjurl;
    window.addEventListener("message", (event) => {
      if (event.data.type === "3DData") {
        // 处理3D数据
        console.log(event.data.data.token, event.data.data.id, "vuehhh");
        localStorage.setItem("tokens", event.data.data.token);
        localStorage.setItem("moxingid", event.data.data.id);
        localStorage.setItem("moids", event.data.data.ids);
        projectId = event.data.data.id;
        that.fetchmodelData1(0);
        that.fetchfenlei();
        // that.getmodeltableData(1, projectId, 0, 0, 0);
        // that.getmodeltableData(2, projectId, 0, 0, 0);

        publicids = event.data.data.ids;
        // that.fetchScene();
        // setTimeout(() => {
        //   getpro(event.data.data.ids);
        // }, 800);
      } else if (event.data.type == 1) {
        if (event.data.data == "复位") {
          resetLayer();
        }
      }
    });
  },
  methods: {
    //放置单个模型
    Placemodel(index, row, event) {
      if (row.placename === "放置模型") {
        row.placename = "保存点位";
        startPlaceOneModel();
      } else {
        row.placename = "放置模型";
        console.log(row, "保存点位");
        
        addonemodel(index, row);
        finishPlaceOneModel();
      }

      console.log(index, row);
      // this.anglemodel = row;
      event.stopPropagation(); // 阻止事件冒泡
      // this.isangclose = true;
    },
    //编辑单个模型的信息
    handleEditmodel(index, row, event) {
      console.log(index, row);
      this.anglemodel = row;
      event.stopPropagation(); // 阻止事件冒泡
      this.isangclose = true;
    },
    handleSubmit() {
      console.log("Modified anglemodel:", this.anglemodel);
      this.updatemodel(this.anglemodel);
      this.isangclose = false;
      // 在这里可以执行提交操作，比如发送请求到后台
    },
    handleCellClick(row, column, cell, event) {
      console.log("点击的行数据:", row);

      let modelId = row.id;
      if (modelId != "") {
        setOutlineById(Number(modelId));
        // zoomToModel(Number(modelId));
      }
    },
    iclose() {
      this.isiclose = false;
    },
    angclose() {
      this.isangclose = false;
    },
    iopen() {
      this.isiclose = true;
    },
    handleSuccess(response, file, fileList) {
      try {
        console.log("上传成功的文件:", file);
        // 使用 URL.createObjectURL 生成临时 URL
        let filePath = URL.createObjectURL(file);
        console.log("生成的文件 URL:", filePath);
        let model = new app3d.model.Model({
          url: filePath,
          decoderPath: "./build/draco/",
        });
        let namelist = [];
        model.on("loaded", function () {
          pipelineData = model.model.children.map((item) => {
            console.log(item);
            let posAttribute = item.geometry.attributes.position,
              index = item.geometry.index && item.geometry.index.array;
            posAttribute.applyMatrix4(item.matrixWorld);
            let posArray = posAttribute.array;
            let result = null;
            if (index) {
              const positions = [];
              for (let i = 0; i < index.length / 2; i++) {
                let prev = index[2 * i],
                  curr = index[2 * i + 1];
                if (i == 0) {
                  positions.push(
                    posArray[3 * prev],
                    posArray[3 * prev + 1],
                    posArray[3 * prev + 2]
                  );
                }
                positions.push(
                  posArray[3 * curr],
                  posArray[3 * curr + 1],
                  posArray[3 * curr + 2]
                );
                // namelist.push(item.name);
                // 检查 item.name 是否已经存在于 namelist 中
                if (!namelist.includes(item.name)) {
                  namelist.push(item.name);
                }
              }
              console.log(positions);
              result = positions;
            } else {
              result = Array.from(posArray);
            }
            return result;
          });
          alert("管道数据获取成功！");
          console.log(pipelineData);
          let savelinearr = [];

          pipelineData.forEach((data, index) => {
            let tubeCoords = [];

            // 从data中提取坐标
            for (let i = 0; i < data.length / 3; i++) {
              tubeCoords.push([data[3 * i], data[3 * i + 1], data[3 * i + 2]]);
            }

            // 生成随机管道ID
            let tubeId = Math.floor(Math.random() * 1000000);

            // 创建savejson对象
            let savejson = {
              useShader: true,
              deviceId: "",
              parkId: 0,
              buildId: 0,
              floorId: 0,
              json: "",
              id: tubeId,
              title: namelist[index],
              type: 2,
              points: tubeCoords,
              direction: 1,
              texturePath:
                "https://3d.dddtask.cn/enginner-xufeng/public-files/pipeline/tubeBg2.png",
              repeatX: 10,
              size: 0.06,
              color: "#aa8921",
              bgColor: namelist[index].includes("fense")
                ? "pink"
                : namelist[index].includes("huangse")
                ? "beige"
                : namelist[index].includes("hongse")
                ? "red"
                : namelist[index].includes("huise")
                ? "gray"
                : namelist[index].includes("zise")
                ? "purple"
                : namelist[index].includes("lvse")
                ? "green"
                : namelist[index].includes("qingse")
                ? "cyan"
                : namelist[index].includes("chengse")
                ? "orange"
                : namelist[index].includes("qianlanse")
                ? "skyBlue"
                : namelist[index].includes("juse")
                ? "orange"
                : namelist[index].includes("qianfense")
                ? "LightPink"
                : "blue",
              isFlyline: true,
              range: 130, //飞线长度
              speed: 3,
            };

            // 将savejson添加到addLinelist
            addLinelist.push(savejson);

            // 打印addLinelist

            // // 以下为注释的代码，如果需要可以取消注释使用
            tubeCoords =
              savejson.direction == 1 ? tubeCoords : tubeCoords.reverse();
            let currTubeMesh = new app3d.mesh.Tube({
              coordinates: tubeCoords,
              iconUrl: savejson.texturePath,
              radius: savejson.size,
              tubularSegments: tubeCoords.length * 10,
              flowSpeed: savejson.repeatX,
            });
            currTubeMesh.mesh.isTube = true;
            tubeMeshes.push(currTubeMesh);
            currTubeMesh.mesh.selectiveModel = currTubeMesh.mesh;
            currTubeMesh.tubeId = tubeId;
            currTubeMesh.modelInfo = savejson;
            view.scene.add(currTubeMesh.mesh);
          });
          console.log(namelist);
          console.log(addLinelist);
          saveLineJSON();
        });
        // 将文件信息添加到 filePaths 数组中
        this.filePaths.push({
          path: filePath,
          id: file.uid, // 使用 file.uid 作为文件 ID
        });
        console.log("上传的文件列表:", this.filePaths);
      } catch (error) {
        console.error("生成文件URL失败:", error);
        this.$message.error("生成文件URL失败");
      }
    },
    beforeUpload(file) {
      console.log("准备上传文件:", file);
      // 检查文件类型
      const isGLB =
        file.type === "model/gltf-binary" || file.name.endsWith(".glb");

      if (!isGLB) {
        this.$message.error("上传文件只能是 .glb 格式!");
        return false;
      }
      return true; // 返回 false 将停止文件上传
    },
    handleRequest(options) {
      // 模拟成功上传
      console.log("模拟文件上传:", options.file);
      this.handleSuccess({}, options.file, options.fileList);
    },
    changeSubmenu(parentMenu, items) {
      console.log(parentMenu, items);
      this.selectedMenu = parentMenu;
      console.log(this.selectedMenu);
      this.submenuItems = items.floor;
      buildId = items.title;
      this.selectedSubmenuItem = null;
    },
    selectSubmenuItem(item) {
      this.selectedSubmenuItem = item;
    },
    labelTitleFun(item, index) {
      // switchbd(index);
      console.log(item, index);
      setfloor(item.name, item.pos, item.tar);
      this.rightBtnIndex2 = index;
      this.rightBtnIndex = null; // 重置楼层选择的状态
    },
    rightBtnFun(item, pos, tar, title, index, index2) {
      let that = this;
      // this.sendMessage(title);
      console.log(index, index2, item, pos, tar, title);
      floorId = title;
      setfloor(item, pos, tar);

      this.getmodeltableData(1, projectId, parkId, buildId, floorId);

      if (index == 0) {
      }
      if (index == 1) {
      } else if (index == 2) {
      } else if (index == 3) {
      }
      // that.rightBtnIndex1 = index;
      that.rightBtnIndex = item;
      that.rightBtnIndex2 = null;
      // if (item[0] == "f5") {
      // setTimeout(() => {
      //   console.log(1111);
      //   addlableldimg(this.roomData);
      // }, 100);
      // }
    },
    onSubmit() {
      console.log(this.sizeForm, "submit!");
      startDrawTube();
      closedetails1();
    },
    changetab(tab, event) {
      console.log(tab.index);
      if (tab.index == 1) {
        this.change2();
      } else {
        this.change1();
      }
    },
    change1() {
      console.log(125);
      let addModelButton = document.getElementById("addModelButton");
      let addLineButton = document.getElementById("addLineButton");
      let upmodel = document.getElementById("upmodel");
      let upline = document.getElementById("upline");
      addModelButton.style.visibility = "visible";
      addLineButton.style.visibility = "hidden";
      upmodel.style.visibility = "visible";
      upline.style.visibility = "hidden";
      document.getElementById("adline").style.visibility = "hidden";
    },
    change2() {
      console.log(225);
      let addModelButton = document.getElementById("addModelButton");
      let addLineButton = document.getElementById("addLineButton");
      let upmodel = document.getElementById("upmodel");
      let upline = document.getElementById("upline");
      addLineButton.style.visibility = "visible";
      addModelButton.style.visibility = "hidden";
      upmodel.style.visibility = "hidden";
      upline.style.visibility = "visible";
      document.getElementById("adline").style.visibility = "visible";
    },
    confirmDelete(index, row) {
      this.$confirm("确定要删除该条记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 确认后的删除操作
          this.handleDelete(index, row);
        })
        .catch(() => {
          // 取消后的处理
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //删除场景模型的接口
    handleDelete(index, row) {
      console.log(index, row);
      this.deletesinglemodel(row.id);
    },
    handleDeleteline(index, row) {
      console.log(index, row);
      this.deletesingleline(row.id);
    },
    handleClick(row) {
      console.log(row);
    },
    handleNodeClick(data) {
      console.log(data.id, "类别id");
      this.fetchmodelData(data.id);
    },
    seedmodelid(data, index) {
      this.active = index;
      console.log(data.ids);
      dynamicId = data.ids;
      mdname = data.name;

      // finishPlaceOneModel();
      // startPlaceOneModel();
    },
    async fetchScene() {
      //产品类别
      try {
        const response = await get(
          `/project/getProjectByIds?id=${publicids}`,
          {}
        );
        console.log(response, "场景数据");
        let data = response.data;
      } catch (error) {
        console.error("GET request error:", error);
      }
    },
    async fetchfenlei() {
      //产品类别
      try {
        const response = await get("/productTag/all", {});
        console.log(response, "产品类别");
        let data = response.data;
        this.jdata = this.jdata1.concat(data);
      } catch (error) {
        console.error("GET request error:", error);
      }
    },
    async fetchmodelData(tagId) {
      //设备模型清单
      try {
        const response = await get("/product/list", {
          page: 1,
          pageSize: 100,
          name: "",
          tagId: tagId,
        });
        console.log(response, "设备模型清单");
        let data = response.data.data;
        this.mlist = data;
        // 使用 for...of 循环和 await
        // for (const item of data) {
        //   await this.getmodelUrl(item.ids);
        // }
        // console.log(pzmodels, "配置model");
        // this.updatepzlist(JSON.stringify(pzmodels));
      } catch (error) {
        console.error("GET request error:", error);
      }
    },
    async fetchmodelData1(tagId) {
      //设备模型清单
      try {
        const response = await get("/product/list", {
          page: 1,
          pageSize: 100,
          name: "",
          tagId: tagId,
        });
        console.log(response, "设备模型清单");
        let data = response.data.data;
        this.mlist = data;
        // 使用 for...of 循环和 await
        for (const item of data) {
          await this.getmodelUrl(item.ids);
        }
        console.log(pzmodels, "配置model");
        this.updatepzlist(JSON.stringify(pzmodels));
      } catch (error) {
        console.error("GET request error:", error);
      }
    },
    //更新poijosn（存放产品列表）
    async updatepzlist(data) {
      try {
        const response = await post("/project/updatePoiJson", {
          id: projectId,
          poijson: data,
        });
        console.log(response, "ssss");
        // addAllModels(glb);
        if (response.code == 200) {
          getpro(publicids);
        }
      } catch (error) {
        console.error("POST request error:", error);
      }
    },
    //获取已经增加的模型列表
    async getmodeltableData(type, projectId, parkId, buildId, floorId) {
      try {
        const response = await get("/projectSet/list", {
          page: 1,
          pageSize: 1500,
          name: "",
          // tagId: 0,
          type: type,
          projectId: projectId,
          deviceId: "",
          parkId: parkId,
          buildId: buildId,
          floorId: floorId,
          roomId: "",
        });
        console.log(response, 1112);
        if (type == 1) {
          // this.modeltableData = response.data.data;
          this.modeltableData = response.data.data.map((item) => ({
            ...item,
            placename: "放置模型", // 初始状态设置为 "放置模型"
          }));
          alreadymodels = response.data.data;
          JSONData.devices = alreadymodels;
          // addBiaoqian()
        } else if (type == 2) {
          let lidata = response.data.data;
          lidata.forEach((obj) => {
            // 提取最外层的 id 值
            let outerId = obj.id;

            // 解析 pzjson 字符串为对象
            let pzjsonObj = JSON.parse(obj.pzjson);

            // 将最外层的 id 值赋给 pzjson 对象
            pzjsonObj.id = outerId;

            // 将修改后的 pzjson 对象重新转化为 JSON 字符串
            obj.pzjson = JSON.stringify(pzjsonObj);
          });

          this.linetableData = lidata.map((item) => {
            return JSON.parse(item.pzjson);
          });
          console.log(this.linetableData);
          alreadylines = lidata.map((item) => {
            return JSON.parse(item.pzjson);
          });
          JSONData.lines = alreadylines;
        }
        // clearScene()
        rebuildScene();
        // JSONData.devices = response.data.data;
      } catch (error) {
        console.error("GET request error:", error);
      }
    },

    async postData() {
      try {
        const response = await post("/your-post-endpoint", { key1: "value1" });
        console.log(response);
      } catch (error) {
        console.error("POST request error:", error);
      }
    },
    //根据ids查询模型地址
    async getmodelUrl(ids) {
      try {
        const response = await get("/product/getProductById", {
          id: ids,
        });
        console.log(response.data, "对应的模型");
        let murl =
          "https://" +
          pjurl +
          ".3dzhanting.cn" +
          "/" +
          JSON.parse(response.data.multiplefiles)[0];
        // console.log(murl, "对应的模型地址");
        let mobj = {
          id: ids,
          path: murl,
        };
        pzmodels.push(mobj);
        console.log(pzmodels, "配置的模型清单");
        JSONData.models = pzmodels;
        console.log(JSONData, "test222");
      } catch (error) {
        console.error("GET request error:", error);
      }
    },
    //新增设备
    async addmodelData(data) {
      console.log(data);
      try {
        const response = await post("/projectSet/add", {
          deviceId: data.deviceId,
          projectId: projectId,
          name: data.name,
          json: data.json,
          parkId: data.parkId,
          buildId: data.buildId,
          floorId: data.floorId,
          type: data.type,
          pzjson: data.pzjson,
        });
        console.log(response);
        if (response) {
          this.getmodeltableData(1, projectId, parkId, buildId, floorId);
          console.log(addDevicelist, JSONData, 256);
          JSONData.devices.concat(addDevicelist);
          console.log(JSONData, 2561);
          addDevicelist = [];
        }
      } catch (error) {
        console.error("POST request error:", error);
      }
    },
    //新增管道数据
    async addlineData(data) {
      console.log(data, "新增管道数据");
      try {
        const response = await post("/projectSet/add", {
          projectId: projectId,
          name: data.name,
          json: data.json,
          parkId: data.parkId,
          buildId: data.buildId,
          floorId: data.floorId,
          type: data.type,
          pzjson: JSON.stringify(data),
        });
        console.log(response);
        if (response) {
          this.getmodeltableData(2, projectId, parkId, buildId, floorId);
          // console.log(addDevicelist, JSONData, 256);
          // JSONData.devices.concat(addDevicelist);
          addLinelist = [];
        }
      } catch (error) {
        console.error("POST request error:", error);
      }
    },
    //删除模型数据
    async deletesinglemodel(id) {
      console.log(id, "模型的id");
      try {
        const response = await post(`/projectSet/delete?id=${id}`, {});
        console.log(response);
        if (response.code == 200) {
          this.getmodeltableData(1, projectId, parkId, buildId, floorId);
          setTimeout(() => {
            rebuildScene();
            // addDevices();
          }, 200);
        }
      } catch (error) {
        console.error("POST request error:", error);
      }
    },
    //删除管道数据
    async deletesingleline(id) {
      console.log(id, "管道的id");
      try {
        const response = await post(`/projectSet/delete?id=${id}`, {});
        console.log(response);
        if (response.code == 200) {
          this.getmodeltableData(2, projectId, parkId, buildId, floorId);
          setTimeout(() => {
            rebuildScene();
            // addDevices();
          }, 200);
        }
      } catch (error) {
        console.error("POST request error:", error);
      }
    },
    //更新模型数据
    async updatemodel(data) {
      console.log(data, "更新模型数据");
      try {
        const response = await post("/projectSet/update", data);
        console.log(response, "更新模型数据成功");
        if (response) {
          this.getmodeltableData(1, projectId, parkId, buildId, floorId);
        }
      } catch (error) {
        console.error("POST request error:", error);
      }
    },

    //更新管道数据
    async updateline(data) {
      const datata = {
        projectId: projectId,

        type: data.type,
        id: data.id,
        pzjson: JSON.stringify(data),
      };
      try {
        const response = await post("/projectSet/update", datata);
        console.log(response, "更新管道数据成功");
        if (response) {
          this.getmodeltableData(2, projectId, parkId, buildId, floorId);
        }
      } catch (error) {
        console.error("POST request error:", error);
      }
    },
  },
});
