<template>
  <div>
    <a-card hoverable :loading="true" style="margin-bottom: 10px" class="card1">
      <template #cover>
        <div class="cover">
          <a-image :src="sourceUrl + props.data.cover" @click="fabu()" :preview="false" />
        </div>
      </template>
      <template #actions>
        <span>
          {{ props.data.name }}
        </span>
        <a-dropdown>
          <a @click.prevent>
            <ellipsis-outlined key="ellipsis" />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <a href="javascript:;" @click="goUpload">
                  <a-space>
                    <EditOutlined />修改描述
                  </a-space></a>
              </a-menu-item>

              <a-menu-item>
                <a href="javascript:;" @click="editguandao">
                  <a-space>
                    <img src="@/assets/images/card/jiaohu.png" class="Ld" alt="Edit" />项目设计
                  </a-space></a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;" @click="equipment">
                  <a-space>
                    <img src="@/assets/images/card/jiaohu.png" class="Ld" alt="Edit" />设备列表
                  </a-space></a>
              </a-menu-item>

              <a-menu-item>
                <a href="javascript:;" @click="fabu()">
                  <a-space>
                    <SendOutlined />项目发布
                  </a-space></a>
              </a-menu-item>

              <a-menu-item>
                <a-popconfirm title="确认删除?" ok-text="是" cancel-text="否" @confirm="confirm" @cancel="cancel">
                  <a href="javascript:;">
                    <a-space>
                      <DeleteOutlined />删除
                    </a-space>
                  </a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </a-card>

    <a-modal v-model:open="open" title="Json 配置" @ok="handleOk"> </a-modal>

    <iframe v-if="show" src="http://127.0.0.1:5500/public/Pipeline-editor/index.html?versions=formal"
      frameborder="0" id="iframe" class="iframest" ref="iframest"></iframe>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { ref, nextTick } from "vue";
import logo from "@/assets/images/logo.png";
const router = useRouter();
const props = defineProps({
  data: {},
  index: "",
});
const guanWangUrl = import.meta.env.VITE_XM_SJ;
const newField = import.meta.env.VITE_NEW_FIELD + "share-project.html";
const iframest = ref(null);
const show = ref(false);
const emit = defineEmits([
  "openChildModal",
  ",restproject",
  "openModeldest",
  "delModel",
]);
import { onMounted, onUnmounted } from "vue";

// 创建一个处理接收消息的函数
function handleReceiveMessage(event) {
  if (event.data.type == "fanhui") {
    console.log(event.data.param);
    show.value = false;
  }
}

onMounted(() => {
  window.addEventListener("message", handleReceiveMessage);
});

onUnmounted(() => {
  window.removeEventListener("message", handleReceiveMessage);
});

const fabu = () => {


  if (!props.data.url) {
    window.open(newField + "?ids=" + encodeURI(props.data.ids));
  } else {
    window.open(props.data.url, "_blank");
  }
};
const triggerMethod = (index) => {
  emit("openChildModal", index);
};

const open = ref(false);
const xiugaixm = () => {
  open.value = true;
};
const handleOk = (e) => {
  console.log(e);
  open.value = false;
};
let sourceUrl = import.meta.env.VITE_SOURCE_URL;

const editMethod = (index) => {
  console.log(index);
  console.log("templates/edit-template/edit-pipeline/index.html");
  // emit("restproject", index);
  location.href =
    window.global.templateUrl +
    "templates/template3/index.html?id=" +
    encodeURI(props.data.ids);
};

const onerror = () => {
  console.log(11);
  return "https://files.3dzhanting.cn/dh3d-upload/1/d2029ba6-0cd2-4b7d-b6f8-335e6e150ffa.png";
};

const openModel = () => {
  emit("openModeldest", props);
  router.push({
    path: "/modelmanagement",
    query: {
      id: props.data.id,
    },
  });
};

const readModels = (props) => {
  console.log(encodeURI(props.data.ids));

  window.open(
    "https://3d.dddtask.cn/enginner-xufeng/scidh3dview-share/index.html" +
    "?ids=" +
    encodeURI(props.data.ids)
  );
};

const cancel = () => {
  console.log(props.data);
};
const confirm = () => {
  emit("delModel", props.data.id);
};
const goUpload = () => {
  router.push({
    path: "/project-added",
    query: {
      id: props.data.id,
    },
  });
};
const handleMessage = (event) => {
  // 检查消息来源和类型
  if (event.data.type === "fanhui") {
    console.log("Received message: ", event.data);
  }
};

const editguandao = () => {
  // 向 iframe 发送消息

  show.value = true;
  nextTick(() => {
    const iframe = document.getElementById('iframe'); // 获取 iframe 元素
    if (iframe) {
      // 确保 iframe 加载完成
      iframe.onload = () => {
        var token = localStorage.getItem('token');
        const message = {
          type: '3DData',
          data: { id: props.data.id, token: token, ids: props.data.ids }
        };
        console.log(message, 'messagemessage');
        iframe.contentWindow.postMessage(message, '*'); // 发送消息
      };
    } else {
      console.error('Iframe 元素未找到');
    }
  });
};

const equipment = () => {
  console.log("设备清单");
  //跳转到/ProjectManagement-equipment
  router.push({
    path: "/project-equipment",
    query: {
      id: props.data.id,
    },
  });
};
</script>

<style lang="scss" scoped>
:deep(.ant-card-cover) {
  padding: 30px;
  width: 100%;
}

.card {
  width: 100%;
}

.cover {
  height: 140px;
  overflow: hidden;
  border-radius: 8px;

  img {
    // width: 100%;
    border-radius: 5px;
  }
}

.Ld {
  width: 15px;
  height: 15px;
}

.iframest {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 2;
  width: 100vw;
  height: 100vh;
  border: 0;
}
</style>